<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alfred - Intelligent Assistant</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">

    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
    />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>

    <!-- Prism.js for syntax highlighting -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>

    <!-- Marked.js for markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>

    <!-- Minidenticons for user avatars -->
    <script>
    // Simple hash function for consistent colors
    function hashCode(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return hash;
    }

    // Generate identicon using canvas
    function generateIdenticon(str, size = 40) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = size;
        canvas.height = size;

        const hash = hashCode(str);
        const color1 = `hsl(${Math.abs(hash) % 360}, 70%, 50%)`;
        const color2 = `hsl(${Math.abs(hash * 2) % 360}, 70%, 70%)`;

        // Create a simple pattern
        ctx.fillStyle = color1;
        ctx.fillRect(0, 0, size, size);

        ctx.fillStyle = color2;
        for (let i = 0; i < 5; i++) {
            for (let j = 0; j < 5; j++) {
                if ((hash >> (i + j * 5)) & 1) {
                    const x = (i * size) / 5;
                    const y = (j * size) / 5;
                    ctx.fillRect(x, y, size / 5, size / 5);
                }
            }
        }

        return canvas.toDataURL();
    }
    </script>

    <style>
        /* CSS Variables for Light and Dark Themes */
        :root {
            /* Light theme (default) */
            --bg-primary: #f8fafc;
            --bg-secondary: #e7edf4;
            --bg-tertiary: #ffffff;
            --text-primary: #0d151c;
            --text-secondary: #49749c;
            --text-muted: #6b7280;
            --border-color: #e7edf4;
            --accent-blue: #0b80ee;
            --accent-blue-hover: #0969d9;
            --hover-bg: #d1d5db;
            --chat-bg-user: #0b80ee;
            --chat-bg-bot: #e7edf4;
            --chat-text-user: #ffffff;
            --chat-text-bot: #0d151c;
            --header-border: #e7edf4;
            --input-bg: #e7edf4;
            --input-text: #0d151c;
            --input-placeholder: #49749c;
        }

        [data-theme="dark"] {
            /* Dark theme */
            --bg-primary: #101a23;
            --bg-secondary: #223649;
            --bg-tertiary: #1e293b;
            --text-primary: #ffffff;
            --text-secondary: #90aecb;
            --text-muted: #64748b;
            --border-color: #223649;
            --accent-blue: #0b80ee;
            --accent-blue-hover: #0969d9;
            --hover-bg: #314d68;
            --chat-bg-user: #0b80ee;
            --chat-bg-bot: #223649;
            --chat-text-user: #ffffff;
            --chat-text-bot: #ffffff;
            --header-border: #223649;
            --input-bg: #223649;
            --input-text: #ffffff;
            --input-placeholder: #90aecb;
        }

        /* Custom styles for functionality preservation */
        .chat-list {
            max-height: 300px;
            overflow-y: auto;
        }
        .chat-list-item {
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
            margin: 2px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: background-color 0.2s;
            color: var(--text-primary);
        }
        .chat-list-item:hover {
            background-color: var(--hover-bg);
        }
        .chat-list-item.active {
            background-color: var(--bg-secondary);
            font-weight: 600;
        }
        .delete-chat-btn {
            display: none;
            background: none;
            border: none;
            color: #9ca3af;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            font-size: 12px;
        }
        .chat-list-item:hover .delete-chat-btn {
            display: inline-block;
        }
        .delete-chat-btn:hover {
            background-color: var(--hover-bg);
        }

        /* Enhanced bot message styling for full-width expansion */
        .bot-message-container {
            width: 100%;
            max-width: none !important;
        }

        .bot-message-content {
            width: 100%;
            max-width: none !important;
            min-height: auto;
            word-wrap: break-word;
            overflow-wrap: break-word;
            white-space: pre-wrap;
        }

        .user-message-content {
            max-width: 500px;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        /* Input box styling to match user message width */
        .input-container {
            max-width: 500px;
            width: 100%;
        }

        /* Ensure proper spacing for long content */
        .message-wrapper {
            width: 100%;
            margin-bottom: 16px;
        }

        /* Beautiful Markdown Styling */
        .markdown-content {
            line-height: 1.7;
            color: var(--chat-text-bot);
            font-size: 15px;
        }

        .markdown-content h1,
        .markdown-content h2,
        .markdown-content h3,
        .markdown-content h4,
        .markdown-content h5,
        .markdown-content h6 {
            font-weight: 600;
            margin-top: 24px;
            margin-bottom: 16px;
            line-height: 1.25;
            color: var(--text-primary);
        }

        .markdown-content h1 {
            font-size: 2em;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 8px;
        }

        .markdown-content h2 {
            font-size: 1.5em;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 6px;
        }

        .markdown-content h3 {
            font-size: 1.25em;
        }

        .markdown-content h4 {
            font-size: 1em;
        }

        .markdown-content h5 {
            font-size: 0.875em;
        }

        .markdown-content h6 {
            font-size: 0.85em;
            color: var(--text-muted);
        }

        .markdown-content p {
            margin-bottom: 16px;
        }

        .markdown-content strong {
            font-weight: 600;
            color: var(--text-primary);
        }

        .markdown-content em {
            font-style: italic;
        }

        .markdown-content ul,
        .markdown-content ol {
            margin-bottom: 16px;
            padding-left: 24px;
        }

        .markdown-content li {
            margin-bottom: 4px;
        }

        .markdown-content li > p {
            margin-bottom: 8px;
        }

        .markdown-content ul li {
            list-style-type: disc;
        }

        .markdown-content ol li {
            list-style-type: decimal;
        }

        .markdown-content blockquote {
            border-left: 4px solid var(--accent-blue);
            padding-left: 16px;
            margin: 16px 0;
            color: var(--text-muted);
            font-style: italic;
            background-color: var(--bg-secondary);
            padding: 16px;
            border-radius: 6px;
        }

        .markdown-content blockquote p {
            margin-bottom: 0;
        }

        .markdown-content table {
            border-collapse: collapse;
            width: 100%;
            margin: 16px 0;
            font-size: 14px;
        }

        .markdown-content table th,
        .markdown-content table td {
            border: 1px solid var(--border-color);
            padding: 8px 12px;
            text-align: left;
        }

        .markdown-content table th {
            background-color: var(--bg-secondary);
            font-weight: 600;
            color: var(--text-primary);
        }

        .markdown-content table tr:nth-child(even) {
            background-color: var(--bg-secondary);
        }

        .markdown-content table tr:hover {
            background-color: var(--hover-bg);
        }

        .markdown-content hr {
            border: none;
            height: 1px;
            background-color: var(--border-color);
            margin: 24px 0;
        }

        .markdown-content a {
            color: var(--accent-blue);
            text-decoration: none;
            border-bottom: 1px solid transparent;
            transition: border-color 0.2s;
        }

        .markdown-content a:hover {
            border-bottom-color: var(--accent-blue);
        }

        .markdown-content code {
            background-color: var(--bg-secondary);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875em;
            color: #e11d48;
            border: 1px solid var(--border-color);
        }

        .markdown-content pre {
            background-color: #1e293b;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            overflow-x: auto;
            position: relative;
            border: 1px solid #334155;
        }

        .markdown-content pre code {
            background: none;
            padding: 0;
            border-radius: 0;
            color: #e2e8f0;
            border: none;
            font-size: 14px;
            line-height: 1.5;
        }

        /* Copy button styling for code blocks */
        .markdown-content pre .copy-to-clipboard-button {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #374151;
            color: #e5e7eb;
            border: 1px solid #4b5563;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
            font-family: inherit;
        }

        .markdown-content pre .copy-to-clipboard-button:hover {
            background: #4b5563;
            border-color: #6b7280;
        }

        /* Custom scrollbar for code blocks */
        .markdown-content pre::-webkit-scrollbar {
            height: 8px;
        }

        .markdown-content pre::-webkit-scrollbar-track {
            background: #334155;
            border-radius: 4px;
        }

        .markdown-content pre::-webkit-scrollbar-thumb {
            background: #64748b;
            border-radius: 4px;
        }

        .markdown-content pre::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Prism.js theme overrides for better integration */
        .markdown-content .token.comment,
        .markdown-content .token.prolog,
        .markdown-content .token.doctype,
        .markdown-content .token.cdata {
            color: #7c8188;
        }

        .markdown-content .token.punctuation {
            color: #c5c8c6;
        }

        .markdown-content .token.property,
        .markdown-content .token.tag,
        .markdown-content .token.boolean,
        .markdown-content .token.number,
        .markdown-content .token.constant,
        .markdown-content .token.symbol,
        .markdown-content .token.deleted {
            color: #f77669;
        }

        .markdown-content .token.selector,
        .markdown-content .token.attr-name,
        .markdown-content .token.string,
        .markdown-content .token.char,
        .markdown-content .token.builtin,
        .markdown-content .token.inserted {
            color: #a6e22e;
        }

        .markdown-content .token.operator,
        .markdown-content .token.entity,
        .markdown-content .token.url,
        .markdown-content .language-css .token.string,
        .markdown-content .style .token.string {
            color: #f8f8f2;
        }

        .markdown-content .token.atrule,
        .markdown-content .token.attr-value,
        .markdown-content .token.keyword {
            color: #ae81ff;
        }

        .markdown-content .token.function {
            color: #66d9ef;
        }

        .markdown-content .token.regex,
        .markdown-content .token.important,
        .markdown-content .token.variable {
            color: #fd971f;
        }

        /* Thinking animation styles */
        .thinking-container {
            background: linear-gradient(90deg, #2563eb 0%, #0ea5e9 25%, #06b6d4 50%, #0ea5e9 75%, #2563eb 100%);
            background-size: 300% 100%;
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: thinking-highlight 3s linear infinite;
            display: block !important;
            text-align: left !important;
            width: 100% !important;
            justify-content: flex-start !important;
            align-items: flex-start !important;
            margin: 0 !important;
            padding: 0 !important;
            float: none !important;
            position: static !important;
        }

        /* Ensure parent container doesn't interfere with thinking alignment */
        .bot-message-content .thinking-container {
            text-align: left !important;
            display: block !important;
            width: 100% !important;
        }

        @keyframes thinking-highlight {
            0% {
                background-position: -300% 0;
            }
            100% {
                background-position: 300% 0;
            }
        }

        /* Placeholder styling */
        input::placeholder {
            color: var(--input-placeholder) !important;
        }

        /* Send button animations */
        .send-button {
            opacity: 0;
            transform: scale(0.8);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            pointer-events: none;
        }

        .send-button.visible {
            opacity: 1;
            transform: scale(1);
            pointer-events: auto;
        }

        .send-button:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="relative flex size-full min-h-screen flex-col group/design-root overflow-x-hidden" style='font-family: Inter, "Noto Sans", sans-serif; background-color: var(--bg-primary);'>
        <div class="layout-container flex h-full grow flex-col">
            <!-- Fixed Header -->
            <header class="fixed top-0 left-0 right-0 z-50 border-b border-solid px-10 py-3" style="background-color: var(--bg-primary); border-bottom-color: var(--header-border);">
                <div class="flex items-center justify-between whitespace-nowrap">
                    <!-- Left: Logo -->
                    <div class="flex items-center gap-4" style="color: var(--text-primary);">
                        <div class="size-4">
                            <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M24 45.8096C19.6865 45.8096 15.4698 44.5305 11.8832 42.134C8.29667 39.7376 5.50128 36.3314 3.85056 32.3462C2.19985 28.361 1.76794 23.9758 2.60947 19.7452C3.451 15.5145 5.52816 11.6284 8.57829 8.5783C11.6284 5.52817 15.5145 3.45101 19.7452 2.60948C23.9758 1.76795 28.361 2.19986 32.3462 3.85057C36.3314 5.50129 39.7376 8.29668 42.134 11.8833C44.5305 15.4698 45.8096 19.6865 45.8096 24L24 24L24 45.8096Z"
                                    fill="currentColor"
                                ></path>
                            </svg>
                        </div>
                        <h2 class="text-lg font-bold leading-tight tracking-[-0.015em]" style="color: var(--text-primary);">Alfred</h2>
                    </div>

                    <!-- Center: Persona Tabs -->
                    <div class="flex items-center gap-2">
                        <a class="persona-tab flex items-center justify-center px-3 py-2 text-xs font-semibold tracking-wide border-b-2 cursor-pointer rounded-t transition-colors" data-persona="business" style="border-bottom-color: var(--accent-blue); color: var(--text-primary);" onmouseover="this.style.backgroundColor=getComputedStyle(document.documentElement).getPropertyValue('--bg-secondary')" onmouseout="this.style.backgroundColor='transparent'">
                            Business
                        </a>
                        <a class="persona-tab flex items-center justify-center px-3 py-2 text-xs font-semibold tracking-wide border-b-2 border-b-transparent cursor-pointer rounded-t transition-colors" data-persona="developer" style="color: var(--text-secondary);" onmouseover="this.style.backgroundColor=getComputedStyle(document.documentElement).getPropertyValue('--bg-secondary')" onmouseout="this.style.backgroundColor='transparent'">
                            Developer
                        </a>
                        <a class="persona-tab flex items-center justify-center px-3 py-2 text-xs font-semibold tracking-wide border-b-2 border-b-transparent cursor-pointer rounded-t transition-colors" data-persona="support" style="color: var(--text-secondary);" onmouseover="this.style.backgroundColor=getComputedStyle(document.documentElement).getPropertyValue('--bg-secondary')" onmouseout="this.style.backgroundColor='transparent'">
                            Support
                        </a>
                    </div>

                    <!-- Session ID Display -->
                    <div id="sessionIdDisplay" class="flex items-center gap-2 px-3 py-1 rounded-lg border" style="background-color: var(--bg-secondary); border-color: var(--border-color); display: none;">
                        <div class="flex items-center gap-2">
                            <span class="text-xs font-medium" style="color: var(--text-secondary);">Session:</span>
                            <span id="sessionIdText" class="text-xs font-mono" style="color: var(--text-primary); max-width: 120px; overflow: hidden; text-overflow: ellipsis;"></span>
                        </div>
                        <button id="copySessionIdBtn" class="flex items-center justify-center w-6 h-6 rounded hover:bg-opacity-80 transition-colors" title="Copy Session ID" style="background-color: var(--hover-bg);" onclick="copySessionId()">
                            <svg class="w-3 h-3" style="color: var(--text-secondary);" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- Right: Buttons -->
                    <div class="flex items-center gap-4">
                        <!-- New Chat Button -->
                        <button id="newChatBtn" class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 text-sm font-bold leading-normal tracking-[0.015em] transition-colors" style="background-color: var(--bg-secondary); color: var(--text-primary);" onmouseover="this.style.backgroundColor=getComputedStyle(document.documentElement).getPropertyValue('--hover-bg')" onmouseout="this.style.backgroundColor=getComputedStyle(document.documentElement).getPropertyValue('--bg-secondary')">
                            <span class="truncate">New Chat</span>
                        </button>
                        <!-- Chat History Dropdown -->
                        <div class="relative">
                            <button id="chatListToggle" class="flex cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 w-10 transition-colors" title="Chat History" style="background-color: var(--bg-secondary); color: var(--text-primary);" onmouseover="this.style.backgroundColor=getComputedStyle(document.documentElement).getPropertyValue('--hover-bg')" onmouseout="this.style.backgroundColor=getComputedStyle(document.documentElement).getPropertyValue('--bg-secondary')">
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </button>
                            <div id="chatListDropdown" class="absolute right-0 mt-2 w-64 rounded-lg shadow-lg border hidden z-50" style="background-color: var(--bg-tertiary); border-color: var(--border-color);">
                                <div class="p-4">
                                    <h3 class="font-semibold mb-2" style="color: var(--text-primary);">Chat History</h3>
                                    <div class="chat-list" id="chatList"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Settings Button -->
                        <div class="relative">
                            <button id="settingsToggle" class="flex cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 w-10 transition-colors" title="Settings" style="background-color: var(--bg-secondary); color: var(--text-primary);" onmouseover="this.style.backgroundColor=getComputedStyle(document.documentElement).getPropertyValue('--hover-bg')" onmouseout="this.style.backgroundColor=getComputedStyle(document.documentElement).getPropertyValue('--bg-secondary')">
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </button>
                            <div id="settingsDropdown" class="absolute right-0 mt-2 w-56 rounded-lg shadow-lg border hidden z-50" style="background-color: var(--bg-tertiary); border-color: var(--border-color);">
                                <div class="p-4">
                                    <h3 class="font-semibold mb-3" style="color: var(--text-primary);">Settings</h3>
                                    <div class="space-y-3">
                                        <div class="flex flex-col">
                                            <span class="text-sm font-medium mb-2" style="color: var(--text-primary);">Theme</span>
                                            <div class="flex flex-col space-y-2">
                                                <label class="flex items-center cursor-pointer">
                                                    <input type="radio" name="theme" value="light" class="mr-2" onchange="setTheme('light')">
                                                    <span class="text-sm" style="color: var(--text-primary);">Light</span>
                                                </label>
                                                <label class="flex items-center cursor-pointer">
                                                    <input type="radio" name="theme" value="dark" class="mr-2" onchange="setTheme('dark')">
                                                    <span class="text-sm" style="color: var(--text-primary);">Dark</span>
                                                </label>
                                                <label class="flex items-center cursor-pointer">
                                                    <input type="radio" name="theme" value="system" class="mr-2" onchange="setTheme('system')">
                                                    <span class="text-sm" style="color: var(--text-primary);">System</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content Area with Fixed Layout -->
            <div class="flex flex-col h-screen pt-20"> <!-- pt-20 to account for fixed header -->

                <!-- Scrollable Chat Messages Area -->
                <div class="flex-1 overflow-y-auto px-40 pt-8 pb-32" id="responseOutput"> <!-- pt-8 for spacing, pb-32 for input area -->
                    <div class="layout-content-container max-w-[960px] mx-auto">
                        <div class="flex items-center justify-center h-full">
                            <span class="text-lg font-normal" style="color: var(--text-secondary);">Insights appear here ✨</span>
                        </div>
                    </div>
                </div>



                <!-- Fixed Input Area at Bottom -->
                <div class="fixed bottom-0 left-0 right-0 z-40 border-t border-solid" style="background-color: var(--bg-primary); border-top-color: var(--header-border);">
                    <!-- Message Input -->
                    <div class="flex items-center px-40 py-3 gap-3 @container">
                        <div class="layout-content-container max-w-[960px] mx-auto w-full">
                            <form class="flex flex-1 items-center gap-3" onsubmit="event.preventDefault(); sendQuery();">
                                <label class="flex flex-col min-w-40 h-12 flex-1">
                                    <div class="flex w-full flex-1 items-stretch rounded-xl h-full">
                                        <input
                                            id="queryInput"
                                            placeholder="Type your query..."
                                            class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl focus:outline-0 focus:ring-0 border-none focus:border-none h-full px-4 rounded-r-none border-r-0 pr-2 text-base font-normal leading-normal" style="background-color: var(--input-bg); color: var(--input-text);"
                                            onkeydown="if(event.key==='Enter'&&!event.shiftKey){event.preventDefault();sendQuery();}"
                                            oninput="toggleSendButton()"
                                            onpaste="setTimeout(toggleSendButton, 10)"
                                            autofocus
                                        />
                                        <div class="flex border-none items-center justify-center pr-4 rounded-r-xl border-l-0 !pr-2" style="background-color: var(--input-bg);">
                                            <div class="flex items-center gap-4 justify-end">
                                                <button type="submit" id="sendButton" class="send-button flex cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 w-8" title="Send message" style="background-color: var(--accent-blue); color: white;" onmouseover="this.style.backgroundColor=getComputedStyle(document.documentElement).getPropertyValue('--accent-blue-hover')" onmouseout="this.style.backgroundColor=getComputedStyle(document.documentElement).getPropertyValue('--accent-blue')">
                                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </label>
                            </form>
                        </div>
                    </div>

                    <!-- Beta disclaimer -->
                    <div id="betaDisclaimer" class="text-center px-4 py-2">
                        <p class="text-sm font-normal leading-normal" style="color: var(--text-secondary);">
                            Alfred is in beta and can make mistakes, so double-check it
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- Hidden elements for compatibility -->
    <select id="personaSelect" style="display: none;">
        <option value="business">Business</option>
        <option value="developer">Developer</option>
        <option value="support">Support</option>
    </select>
    <script>
        // Theme management
        let systemThemeMediaQuery = null;

        function getSystemTheme() {
            return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        }

        function applyTheme(theme) {
            const actualTheme = theme === 'system' ? getSystemTheme() : theme;
            document.documentElement.setAttribute('data-theme', actualTheme);

            // Update button colors immediately
            updateButtonColors();

            // Update input placeholder color
            updateInputPlaceholder();
        }

        function setTheme(theme) {
            localStorage.setItem('theme', theme);
            applyTheme(theme);
            updateThemeRadios();

            // Handle system theme listener
            if (theme === 'system') {
                if (!systemThemeMediaQuery) {
                    systemThemeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
                    systemThemeMediaQuery.addEventListener('change', handleSystemThemeChange);
                }
            } else {
                if (systemThemeMediaQuery) {
                    systemThemeMediaQuery.removeEventListener('change', handleSystemThemeChange);
                    systemThemeMediaQuery = null;
                }
            }
        }

        function handleSystemThemeChange() {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'system') {
                applyTheme('system');
            }
        }

        function updateThemeRadios() {
            const savedTheme = localStorage.getItem('theme') || 'system';
            const radios = document.querySelectorAll('input[name="theme"]');
            radios.forEach(radio => {
                radio.checked = radio.value === savedTheme;
            });
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'system';
            applyTheme(savedTheme);
            updateThemeRadios();

            // Set up system theme listener if needed
            if (savedTheme === 'system') {
                systemThemeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
                systemThemeMediaQuery.addEventListener('change', handleSystemThemeChange);
            }
        }

        function updateInputPlaceholder() {
            const input = document.getElementById('queryInput');
            if (input) {
                const placeholderColor = getComputedStyle(document.documentElement).getPropertyValue('--input-placeholder');
                input.style.setProperty('--placeholder-color', placeholderColor);
            }
        }

        function updateButtonColors() {
            // Get current CSS variable values
            const bgSecondary = getComputedStyle(document.documentElement).getPropertyValue('--bg-secondary');
            const textPrimary = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');

            // Update New Chat button
            const newChatBtn = document.getElementById('newChatBtn');
            if (newChatBtn) {
                newChatBtn.style.backgroundColor = bgSecondary;
                newChatBtn.style.color = textPrimary;
            }

            // Update Chat History button
            const chatListToggle = document.getElementById('chatListToggle');
            if (chatListToggle) {
                chatListToggle.style.backgroundColor = bgSecondary;
                chatListToggle.style.color = textPrimary;
            }

            // Update Settings button
            const settingsToggle = document.getElementById('settingsToggle');
            if (settingsToggle) {
                settingsToggle.style.backgroundColor = bgSecondary;
                settingsToggle.style.color = textPrimary;
            }

            // Update Send button
            const sendButton = document.getElementById('sendButton');
            if (sendButton) {
                const accentBlue = getComputedStyle(document.documentElement).getPropertyValue('--accent-blue');
                sendButton.style.backgroundColor = accentBlue;
                sendButton.style.color = 'white';
            }
        }

        function toggleSettings() {
            const dropdown = document.getElementById('settingsDropdown');
            dropdown.classList.toggle('hidden');
        }

        // User management
        function getUserId() {
            let userId = localStorage.getItem('userId');
            if (!userId) {
                userId = 'user-' + Date.now() + '-' + Math.random().toString(36).substring(2, 9);
                localStorage.setItem('userId', userId);
            }
            return userId;
        }

        // Generate user identicon
        function getUserAvatar(userId) {
            try {
                const imageData = generateIdenticon(userId, 40);
                return `<img src="${imageData}" alt="User Avatar" class="w-full h-full rounded-full">`;
            } catch (error) {
                console.error('Error generating identicon:', error);
                return '<span class="text-white font-semibold text-sm">U</span>';
            }
        }

        // Send button visibility control
        function toggleSendButton() {
            const queryInput = document.getElementById('queryInput');
            const sendButton = document.getElementById('sendButton');

            if (queryInput && sendButton) {
                const hasContent = queryInput.value.trim().length > 0;
                if (hasContent) {
                    sendButton.classList.add('visible');
                } else {
                    sendButton.classList.remove('visible');
                }
            }
        }

        // New UI functionality
        function toggleChatList() {
            const dropdown = document.getElementById('chatListDropdown');
            dropdown.classList.toggle('hidden');
        }

        // Task Management Functions (Inline)

        function getStatusIcon(status) {
            switch (status) {
                case 'pending':
                    return `<div class="w-4 h-4 rounded-full" style="background-color: #9ca3af;"></div>`;
                case 'in_progress':
                    return `<div class="w-4 h-4 rounded-full" style="background-color: var(--accent-blue);">
                        <div class="w-2 h-2 rounded-full bg-white animate-pulse mx-auto mt-1"></div>
                    </div>`;
                case 'completed':
                    return `<div class="w-4 h-4 rounded-full flex items-center justify-center" style="background-color: #10b981;">
                        <svg class="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>`;
                case 'failed':
                    return `<div class="w-4 h-4 rounded-full flex items-center justify-center" style="background-color: #ef4444;">
                        <svg class="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </div>`;
                case 'cancelled':
                    return `<div class="w-4 h-4 rounded-full" style="background-color: #6b7280;"></div>`;
                default:
                    return `<div class="w-4 h-4 rounded-full" style="background-color: #9ca3af;"></div>`;
            }
        }

        function getStatusColor(status) {
            switch (status) {
                case 'pending': return '#9ca3af';
                case 'in_progress': return 'var(--accent-blue)';
                case 'completed': return '#10b981';
                case 'failed': return '#ef4444';
                case 'cancelled': return '#6b7280';
                default: return '#9ca3af';
            }
        }

        // Note: Old panel functions removed - now using inline task display

        async function fetchTaskStatus(sessionId) {
            try {
                const response = await fetch(`/sessions/${sessionId}/tasks`);
                const data = await response.json();
                return data;
            } catch (error) {
                console.error('Error fetching task status:', error);
                return null;
            }
        }

        function renderTaskListInline(tasks, summary) {
            const taskSummaryElement = document.getElementById('task-summary-inline');
            const taskCurrentElement = document.getElementById('task-current');
            const taskCurrentText = document.getElementById('task-current-text');
            const taskProgressBar = document.getElementById('task-progress-bar');
            const taskProgressFill = document.getElementById('task-progress-fill');

            if (!taskSummaryElement) {
                console.warn('Inline task elements not found');
                return;
            }

            // Update summary
            const completed = summary.completed_tasks?.length || 0;
            const total = summary.total_tasks || 0;

            if (total === 0) {
                taskSummaryElement.textContent = 'Planning...';
                hideTaskElements();
                return;
            }

            // Update progress summary and bar
            taskSummaryElement.textContent = `${completed}/${total}`;

            if (taskProgressBar && taskProgressFill) {
                const progressPercent = total > 0 ? (completed / total) * 100 : 0;
                taskProgressBar.classList.remove('hidden');
                taskProgressFill.style.width = `${progressPercent}%`;
            }

            if (!tasks || tasks.length === 0) {
                hideTaskElements();
                return;
            }

                        // Status emoji mapping
            const statusEmojis = {
                'pending': '⏳',
                'in_progress': '🔄',
                'completed': '✅',
                'failed': '❌',
                'cancelled': '🚫'
            };

            // Build display for all tasks in their original order (do not reorder by status)
            let allTasksDisplay = '';

            // Show all tasks in their original planned order, regardless of completion status
            tasks.forEach(task => {
                const emoji = statusEmojis[task.status] || '❓';
                // Increased truncation from 80 to 150 characters
                const description = task.description.length > 150
                    ? task.description.substring(0, 150) + '...'
                    : task.description;

                allTasksDisplay += `<div class="flex items-start gap-2 text-xs mb-1" style="color: var(--text-secondary);">
                    <span class="text-sm">${emoji}</span>
                    <span class="flex-1 markdown-content">${renderMarkdown(description)}</span>
                </div>`;
            });

            // Update the current task element to show all tasks
            if (taskCurrentElement && taskCurrentText) {
                taskCurrentElement.classList.remove('hidden');
                taskCurrentText.innerHTML = allTasksDisplay;
            }
        }

        function hideTaskElements() {
            // Helper function to hide task elements when no tasks are available
            const elementsToHide = ['task-current', 'task-progress-bar'];
            elementsToHide.forEach(id => {
                const element = document.getElementById(id);
                if (element) element.classList.add('hidden');
            });
        }



        // Task polling during agent processing
        let taskPollingInterval = null;

                function startTaskPolling(sessionId) {
            if (taskPollingInterval) {
                clearInterval(taskPollingInterval);
            }

            taskPollingInterval = setInterval(async () => {
                const taskData = await fetchTaskStatus(sessionId);
                if (taskData && taskData.tasks) {
                    renderTaskListInline(taskData.tasks, taskData.summary);
                } else if (taskData && taskData.tasks && taskData.tasks.length === 0) {
                    // Tasks may have been archived - show planning state
                    const taskSummaryElement = document.getElementById('task-summary-inline');
                    if (taskSummaryElement) {
                        taskSummaryElement.textContent = 'Planning...';
                    }
                    hideTaskElements();
                }
            }, 1000); // Poll every second
        }

        function stopTaskPolling() {
            if (taskPollingInterval) {
                clearInterval(taskPollingInterval);
                taskPollingInterval = null;
            }
        }

        function selectPersona(persona) {
            // Update the hidden select element for compatibility
            document.getElementById('personaSelect').value = persona;

            // Update tab styling
            document.querySelectorAll('.persona-tab').forEach(tab => {
                const tabPersona = tab.getAttribute('data-persona');

                if (tabPersona === persona) {
                    // Active tab styling
                    tab.style.borderBottomColor = 'var(--accent-blue)';
                    tab.style.color = 'var(--text-primary)';
                } else {
                    // Inactive tab styling
                    tab.style.borderBottomColor = 'transparent';
                    tab.style.color = 'var(--text-secondary)';
                }
            });
        }

        // Initialize persona tabs with proper state
        function initializePersonaTabs() {
            const personaTabs = document.querySelectorAll('.persona-tab');
            const defaultPersona = 'business';

            // Set up click event listeners
            personaTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const persona = this.getAttribute('data-persona');
                    selectPersona(persona);

                    // Update the current session's persona if it's allowed
                    if (currentSessionId && chatSessions[currentSessionId]) {
                        const session = chatSessions[currentSessionId];
                        const hasUserMessage = session.history && session.history.some(m => m.type === 'user');
                        if (!hasUserMessage) {
                            chatSessions[currentSessionId].persona = persona;
                            saveChatToServer(currentSessionId).catch(console.error);
                        }
                    }
                });
            });

            // Set initial state
            selectPersona(defaultPersona);
        }

        // --- Server persistence helpers ---
        async function fetchChatsFromServer() {
            const userId = getUserId();
            // Fetch non-archived sessions for the current user from the new sessions API
            const res = await fetch(`/sessions?user_id=${userId}&limit=100`);
            const data = await res.json();

            // Convert sessions format to the expected format for backward compatibility
            const chats = {};
            if (data.sessions) {
                data.sessions.forEach(session => {
                    // Only include non-archived sessions (check both archived field and status)
                    const isArchived = session.archived || session.status === 'archived';
                    if (!isArchived) {
                        chats[session.session_id] = {
                            title: session.title || 'Untitled Session',
                            history: session.history || [],  // Database returns 'history', not 'messages'
                            persona: session.persona || 'business',
                            feedback: session.feedback || [],
                            archived: false,
                            user_id: session.user_id
                        };
                    }
                });
            }
            return chats;
        }

        async function saveChatToServer(sessionId) {
            const chat = chatSessions[sessionId];
            if (!chat) return;

            // Only save to server if the chat has content (messages) or has been saved before
            if (chat.history.length === 0 && !chat.savedToServer) {
                return; // Don't save empty chats to server
            }

            // Save to MongoDB via the sessions system (this will happen automatically
            // when messages are sent through the /ask endpoint, but we can still
            // call this for explicit saves if needed)

            // Note: The actual session saving is now handled by the backend
            // when messages are processed through /ask endpoint
            // This function is kept for compatibility but doesn't need to do much

            // Mark as saved to server
            chat.savedToServer = true;
        }

        async function archiveChatOnServer(sessionId) {
            await fetch(`/sessions/${sessionId}/archive`, { method: 'POST' });
        }

        let chatSessions = {};
        let currentSessionId = null;
        const pendingRequests = new Map();
        let chatsLoading = true;

        // Robust auto-scroll function
        function scrollToBottom(responseBox, element = null) {
            // Multiple approaches to ensure scrolling works
            setTimeout(() => {
                // Method 1: Direct scroll to bottom
                responseBox.scrollTop = responseBox.scrollHeight;

                // Method 2: Scroll specific element into view
                if (element) {
                    element.scrollIntoView({ behavior: 'smooth', block: 'end' });
                } else {
                    // Method 3: Scroll the last child into view
                    const lastChild = responseBox.lastElementChild;
                    if (lastChild) {
                        lastChild.scrollIntoView({ behavior: 'smooth', block: 'end' });
                    }
                }

                // Method 4: Force scroll after a short delay
                setTimeout(() => {
                    responseBox.scrollTop = responseBox.scrollHeight;
                }, 50);
            }, 10);
        }

        // Function to clean up markdown formatting issues
        function cleanMarkdownContent(content) {
            if (!content) return content;

            // Remove excessive newlines before bullet points
            // Pattern: newline + newline + bullet point
            content = content.replace(/\n\n(\s*[*•-]\s)/g, '\n$1');

            // Remove excessive newlines before numbered lists
            content = content.replace(/\n\n(\s*\d+\.\s)/g, '\n$1');

            // Clean up multiple consecutive newlines (more than 2)
            content = content.replace(/\n{3,}/g, '\n\n');

            return content;
        }

        // Function to render markdown text to HTML
        function renderMarkdown(markdownText) {
            if (!markdownText) return '';

            console.log('renderMarkdown called with:', markdownText.substring(0, 100) + '...');
            console.log('marked available:', typeof marked !== 'undefined');

            // Configure marked options for better rendering
            if (typeof marked !== 'undefined') {
                try {
                    // Check if marked is a function (older versions) or has parse method (newer versions)
                    let result;
                    if (typeof marked === 'function') {
                        console.log('Using marked() function');
                        marked.setOptions({
                            highlight: function(code, lang) {
                                if (typeof Prism !== 'undefined' && lang && Prism.languages[lang]) {
                                    return Prism.highlight(code, Prism.languages[lang], lang);
                                }
                                return code;
                            },
                            breaks: true,
                            gfm: true,
                            tables: true,
                            sanitize: false
                        });
                        result = marked(markdownText);
                    } else if (marked.parse && typeof marked.parse === 'function') {
                        console.log('Using marked.parse() method');
                        marked.setOptions({
                            highlight: function(code, lang) {
                                if (typeof Prism !== 'undefined' && lang && Prism.languages[lang]) {
                                    return Prism.highlight(code, Prism.languages[lang], lang);
                                }
                                return code;
                            },
                            breaks: true,
                            gfm: true,
                            tables: true,
                            sanitize: false
                        });
                        result = marked.parse(markdownText);
                    } else {
                        console.error('marked object found but no parse method or function available');
                        throw new Error('marked API not recognized');
                    }

                    console.log('marked result:', result.substring(0, 100) + '...');
                    return result;
                } catch (error) {
                    console.error('Error rendering markdown:', error);
                    return markdownText;
                }
            }

            console.log('marked not available, using fallback');
            // Fallback if marked is not available - just escape HTML and convert line breaks
            return markdownText
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/\n/g, '<br>');
        }

        // Function to process markdown content and apply syntax highlighting
        function processMarkdownContent() {
            // Apply syntax highlighting to code blocks
            if (typeof Prism !== 'undefined') {
                Prism.highlightAll();
            }

            // Add copy buttons to code blocks if not already present
            const codeBlocks = document.querySelectorAll('.markdown-content pre:not(.copy-enabled)');
            codeBlocks.forEach(block => {
                if (!block.querySelector('.copy-to-clipboard-button')) {
                    const copyButton = document.createElement('button');
                    copyButton.className = 'copy-to-clipboard-button';
                    copyButton.textContent = 'Copy';
                    copyButton.onclick = () => copyCodeToClipboard(block, copyButton);
                    block.appendChild(copyButton);
                    block.classList.add('copy-enabled');
                }
            });
        }

        // Function to copy code to clipboard
        function copyCodeToClipboard(codeBlock, button) {
            const code = codeBlock.querySelector('code');
            if (code) {
                const text = code.textContent;
                navigator.clipboard.writeText(text).then(() => {
                    const originalText = button.textContent;
                    button.textContent = 'Copied!';
                    button.style.backgroundColor = '#22c55e';
                    setTimeout(() => {
                        button.textContent = originalText;
                        button.style.backgroundColor = '';
                    }, 2000);
                }).catch(err => {
                    console.error('Failed to copy: ', err);
                    button.textContent = 'Failed';
                    setTimeout(() => {
                        button.textContent = 'Copy';
                    }, 2000);
                });
            }
        }

        document.addEventListener('DOMContentLoaded', async () => {
            // Original initialization
            document.getElementById('newChatBtn').addEventListener('click', createNewChat);

            // New UI initialization
            document.getElementById('chatListToggle').addEventListener('click', toggleChatList);
            document.getElementById('settingsToggle').addEventListener('click', toggleSettings);

            // Initialize theme
            initializeTheme();

            // Persona tab clicks
            document.querySelectorAll('.persona-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    const persona = this.getAttribute('data-persona');
                    selectPersona(persona);

                    // Update the current session's persona if it's allowed
                    if (currentSessionId && chatSessions[currentSessionId]) {
                        const session = chatSessions[currentSessionId];
                        const hasUserMessage = session.history && session.history.some(m => m.type === 'user');
                        if (!hasUserMessage) {
                            chatSessions[currentSessionId].persona = persona;
                            saveChatToServer(currentSessionId).catch(console.error);
                        }
                    }
                });
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(event) {
                const chatDropdown = document.getElementById('chatListDropdown');
                const chatToggle = document.getElementById('chatListToggle');
                const settingsDropdown = document.getElementById('settingsDropdown');
                const settingsToggle = document.getElementById('settingsToggle');

                if (!chatDropdown.contains(event.target) && !chatToggle.contains(event.target)) {
                    chatDropdown.classList.add('hidden');
                }

                if (!settingsDropdown.contains(event.target) && !settingsToggle.contains(event.target)) {
                    settingsDropdown.classList.add('hidden');
                }
            });

            // Add scroll to bottom when input is focused
            const queryInput = document.getElementById('queryInput');
            queryInput.addEventListener('focus', () => {
                const responseBox = document.getElementById('responseOutput');
                scrollToBottom(responseBox);
            });

            // Test marked.js functionality
            console.log('Testing marked.js...');
            console.log('marked available, typeof marked:', typeof marked);
            if (typeof marked !== 'undefined') {
                console.log('marked.parse available:', typeof marked.parse);
                try {
                    const testMarkdown = '**Hello** *world*!';
                    let testResult;
                    if (typeof marked === 'function') {
                        console.log('Using marked() function');
                        testResult = marked(testMarkdown);
                    } else if (marked.parse && typeof marked.parse === 'function') {
                        console.log('Using marked.parse() method');
                        testResult = marked.parse(testMarkdown);
                    }
                    console.log('marked test result:', testResult);
                } catch (error) {
                    console.error('marked test error:', error);
                }
            }

            await loadChats();
        });

        async function loadChats() {
            chatsLoading = true;
            renderCurrentChat();
            try {
                const chats = await fetchChatsFromServer();
                chatSessions = chats;

                // Ensure feedback array exists for all chats and mark them as saved
                Object.values(chatSessions).forEach(chat => {
                    if (!chat.feedback) {
                        chat.feedback = [];
                    }
                    if (!chat.archived) {
                        chat.archived = false;
                    }
                    // Mark chats loaded from server as saved
                    chat.savedToServer = true;
                });

                // Always create a new chat on page load/refresh
                // This keeps existing chats in history but starts fresh
                renderChatList();
                await createNewChat();
                updateSessionIdDisplay();
            } catch (error) {
                console.error('Error loading chats:', error);
                // Create new chat even if loading existing chats failed
                await createNewChat();
                updateSessionIdDisplay();
            }
            chatsLoading = false;

            // Focus input field when chat is ready
            setTimeout(() => {
                const input = document.getElementById('queryInput');
                if (input) {
                    input.focus();
                }
            }, 100);
        }

        async function createNewChat() {
            const sessionId = `session-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
            const persona = document.getElementById('personaSelect').value || 'business';
            chatSessions[sessionId] = {
                history: [],
                feedback: [],
                title: 'New Chat',
                persona: persona,
                archived: false,
                user_id: getUserId(),
                savedToServer: false  // Track if this session has been saved to server
            };
            currentSessionId = sessionId;
            // Don't save to server immediately - wait until user sends first message
            renderChatList();
            renderCurrentChat();
            updateSessionIdDisplay();

            // Ensure persona tabs are updated
            selectPersona(persona);

            // Focus the input field for immediate use
            setTimeout(() => {
                const input = document.getElementById('queryInput');
                if (input) {
                    input.focus();
                }
            }, 100);
        }

        async function switchChat(sessionId) {
            currentSessionId = sessionId;
            renderCurrentChat();
            renderChatList();
            updateSessionIdDisplay();
            if (chatSessions[sessionId]) {
                const persona = chatSessions[sessionId].persona || 'business';
                document.getElementById('personaSelect').value = persona;
                selectPersona(persona);
            } else {
                // Ensure default persona is set for new sessions
                selectPersona('business');
            }
            setPersonaSelectorState();

            // Close chat list dropdown
            document.getElementById('chatListDropdown').classList.add('hidden');
        }

        async function archiveChat(sessionId) {
            if (!chatSessions[sessionId]) return;

            const chat = chatSessions[sessionId];

            // Only archive on server if the chat was saved to server
            if (chat.savedToServer) {
                chatSessions[sessionId].archived = true;
                await archiveChatOnServer(sessionId);
            }

            // Remove from local state since we don't show archived chats
            delete chatSessions[sessionId];
            if (currentSessionId === sessionId) {
                const keys = Object.keys(chatSessions);
                currentSessionId = keys.length > 0 ? keys[0] : null;
            }
            renderChatList();
            renderCurrentChat();
        }

        function renderChatList() {
            const chatList = document.getElementById('chatList');
            chatList.innerHTML = '';

            // Sort chats by creation time (most recent first)
            // Extract session IDs and sort by timestamp embedded in the ID
            const sortedSessionIds = Object.keys(chatSessions)
                .filter(sessionId => !chatSessions[sessionId].archived) // Skip archived chats
                .sort((a, b) => {
                    // Extract timestamp from session ID (format: session-{timestamp}-{random})
                    const timestampA = parseInt(a.split('-')[1]) || 0;
                    const timestampB = parseInt(b.split('-')[1]) || 0;
                    return timestampB - timestampA; // Descending order (newest first)
                });

            for (const sessionId of sortedSessionIds) {
                const chat = chatSessions[sessionId];

                const item = document.createElement('li');
                item.className = 'chat-list-item';
                if (sessionId === currentSessionId) {
                    item.classList.add('active');
                }

                // Chat title
                const titleSpan = document.createElement('span');
                titleSpan.textContent = chat.title;
                titleSpan.style.flex = '1';
                titleSpan.style.overflow = 'hidden';
                titleSpan.style.textOverflow = 'ellipsis';
                titleSpan.style.whiteSpace = 'nowrap';
                item.appendChild(titleSpan);

                // Delete (archive) button
                const deleteBtn = document.createElement('button');
                deleteBtn.innerHTML = `
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                `;
                deleteBtn.title = 'Delete chat';
                deleteBtn.className = 'delete-chat-btn';

                // Prevent deletion of currently active chat
                if (sessionId === currentSessionId) {
                    deleteBtn.style.opacity = '0.3';
                    deleteBtn.style.cursor = 'not-allowed';
                    deleteBtn.title = 'Cannot delete the currently active chat';
                    deleteBtn.onclick = (e) => {
                        e.stopPropagation();
                        // Do nothing - prevent deletion of active chat
                    };
                } else {
                    deleteBtn.onclick = async (e) => {
                        e.stopPropagation();
                        await archiveChat(sessionId);
                    };
                }

                item.appendChild(deleteBtn);

                item.setAttribute('data-session-id', sessionId);
                item.addEventListener('click', () => switchChat(sessionId));
                chatList.appendChild(item);
            }
        }

        function setPersonaSelectorState() {
            const personaSelect = document.getElementById('personaSelect');
            const personaTabs = document.querySelectorAll('.persona-tab');

            // Default to enabled state
            let shouldEnable = true;

            // Only disable if we have a current session with user messages
            if (currentSessionId && chatSessions[currentSessionId]) {
                const session = chatSessions[currentSessionId];
                // Check if there are ANY user messages in the history
                const hasUserMessage = session.history && session.history.length > 0 &&
                    session.history.some(m => m.type === 'user');
                shouldEnable = !hasUserMessage;
            }

            // Apply the state
            personaSelect.disabled = !shouldEnable;

            if (shouldEnable) {
                personaTabs.forEach(tab => {
                    tab.style.pointerEvents = 'auto';
                    tab.style.opacity = '1';
                    tab.style.cursor = 'pointer';
                    tab.title = 'Choose how the agent should answer your questions for this chat.';
                });
            } else {
                personaTabs.forEach(tab => {
                    tab.style.pointerEvents = 'none';
                    tab.style.opacity = '0.5';
                    tab.style.cursor = 'not-allowed';
                    tab.title = 'Persona can only be set before the first message in a chat.';
                });
            }
        }

        function renderCurrentChat() {
            const responseBox = document.getElementById('responseOutput');
            if (chatsLoading) {
                responseBox.innerHTML = '<div class="flex items-center justify-center h-full"><div class="text-center"><span class="text-lg font-normal" style="color: var(--text-secondary);">Hello! I\'m Alfred, your intelligent assistant.</span><br><span class="text-base font-normal mt-2" style="color: var(--text-secondary);">Ask me anything about your logs and data!</span></div></div>';
                setPersonaSelectorState();
                return;
            }
            if (!currentSessionId || !chatSessions[currentSessionId]) {
                responseBox.innerHTML = '<div class="flex items-center justify-center h-full"><div class="text-center"><span class="text-lg font-normal" style="color: var(--text-secondary);">Hello! I\'m Alfred, your intelligent assistant.</span><br><span class="text-base font-normal mt-2" style="color: var(--text-secondary);">Ask me anything about your logs and data!</span></div></div>';
                setPersonaSelectorState();
                return;
            }
            const session = chatSessions[currentSessionId];
            responseBox.innerHTML = '';

            if (session.history.length === 0) {
                responseBox.innerHTML = '<div class="flex items-center justify-center h-full"><div class="text-center"><span class="text-lg font-normal" style="color: var(--text-secondary);">Hello! I\'m Alfred, your intelligent assistant.</span><br><span class="text-base font-normal mt-2" style="color: var(--text-secondary);">Ask me anything about your logs and data!</span></div></div>';
            } else {
                session.history.forEach((msg, index) => {
                    if (msg.type === 'user') {
                        // User message (right side)
                        const messageContainer = document.createElement('div');
                        messageContainer.className = 'message-wrapper flex items-start gap-3 p-4 justify-end';
                        messageContainer.innerHTML = `
                            <div class="flex flex-1 flex-col gap-1 items-end">
                                <div class="user-message-content text-base font-normal leading-normal rounded-xl px-4 py-3" style="background-color: var(--chat-bg-user); color: var(--chat-text-user);">${msg.content}</div>
                            </div>
                            <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 h-10 shrink-0 overflow-hidden user-avatar flex items-center justify-center" style="background-color: #e5e7eb;">
                            </div>
                        `;
                        responseBox.appendChild(messageContainer);

                        // Generate and insert user identicon
                        const userId = getUserId();
                        const avatarContainer = messageContainer.querySelector('.user-avatar');
                        if (avatarContainer) {
                            avatarContainer.innerHTML = getUserAvatar(userId);
                        }
                    } else {
                        // Bot message (left side) - Full width expandable
                        const messageContainer = document.createElement('div');
                        messageContainer.className = 'message-wrapper bot-message-container';

                        // Build task execution summary if it exists
                        let taskSummaryHTML = '';
                        if (msg.task_execution_summary && msg.task_execution_summary.tasks && msg.task_execution_summary.tasks.length > 0) {
                            const statusEmojis = {
                                'pending': '⏳',
                                'in_progress': '🔄',
                                'completed': '✅',
                                'failed': '❌',
                                'cancelled': '🚫'
                            };

                            let allTasksHTML = '';
                            msg.task_execution_summary.tasks.forEach(task => {
                                const emoji = statusEmojis[task.status] || '❓';
                                const description = task.description.length > 150
                                    ? task.description.substring(0, 150) + '...'
                                    : task.description;

                                allTasksHTML += `<div class="flex items-start gap-2 text-xs mb-1" style="color: var(--text-secondary);">
                                    <span class="text-sm">${emoji}</span>
                                    <span class="flex-1 markdown-content">${renderMarkdown(description)}</span>
                                </div>`;
                            });

                            taskSummaryHTML = `
                                <div class="mb-3 p-3 rounded-lg border" style="background-color: var(--bg-secondary); border-color: var(--border-color);">
                                    <div class="text-xs font-medium mb-2" style="color: var(--text-primary);">Task Execution Summary</div>
                                    <div class="mx-4 mt-1 text-xs" style="color: var(--text-secondary);">
                                        <span>${allTasksHTML}</span>
                                    </div>
                                </div>`;
                        }

                        messageContainer.innerHTML = `
                            <div class="flex items-start gap-3 p-4 w-full">
                                <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 h-10 shrink-0 bg-blue-500 flex items-center justify-center">
                                    <span class="text-white font-semibold">A</span>
                                </div>
                                <div class="flex flex-col gap-1 flex-1 min-w-0 w-full">
                                    ${taskSummaryHTML}
                                    <div class="rounded-xl px-4 py-3" style="background-color: var(--chat-bg-bot);">
                                        <div class="bot-message-content markdown-content bot-message-text">${renderMarkdown(msg.content)}</div>
                                    </div>
                                </div>
                            </div>
                        `;
                        responseBox.appendChild(messageContainer);

                        // Add feedback buttons after bot messages
                        const feedbackContainer = document.createElement('div');
                        feedbackContainer.className = '@container ml-14';
                        feedbackContainer.innerHTML = `
                            <div class="gap-2 px-4 flex flex-wrap justify-start">
                                <div class="flex flex-col items-center gap-2 py-2.5 text-center w-20 cursor-pointer" style="background-color: transparent; color: var(--text-primary);" onmouseover="this.style.backgroundColor='var(--bg-tertiary)'" onmouseout="this.style.backgroundColor='transparent'" onclick="submitQuickFeedback('helpful', ${index})" aria-label="Mark as helpful">
                                    <div class="rounded-full p-2.5" style="background-color: var(--bg-tertiary);">
                                        <div style="color: var(--text-primary);" data-icon="ThumbsUp" data-size="20px" data-weight="regular">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                                                <path d="M234,80.12A24,24,0,0,0,216,72H160V56a40,40,0,0,0-40-40,8,8,0,0,0-7.16,4.42L75.06,96H32a16,16,0,0,0-16,16v88a16,16,0,0,0,16,16H204a24,24,0,0,0,23.82-21l12-96A24,24,0,0,0,234,80.12ZM32,112H72v88H32ZM223.94,97l-12,96a8,8,0,0,1-7.94,7H88V105.89l36.71-73.43A24,24,0,0,1,144,56V80a8,8,0,0,0,8,8h64a8,8,0,0,1,7.94,9Z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex flex-col items-center gap-2 py-2.5 text-center w-20 cursor-pointer" style="background-color: transparent; color: var(--text-primary);" onmouseover="this.style.backgroundColor='var(--bg-tertiary)'" onmouseout="this.style.backgroundColor='transparent'" onclick="submitQuickFeedback('not helpful', ${index})">
                                    <div class="rounded-full p-2.5" style="background-color: var(--bg-tertiary);">
                                        <div style="color: var(--text-primary);" data-icon="ThumbsDown" data-size="20px" data-weight="regular">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                                                <path d="M239.82,157l-12-96A24,24,0,0,0,204,40H32A16,16,0,0,0,16,56v88a16,16,0,0,0,16,16H75.06l37.78,75.58A8,8,0,0,0,120,240a40,40,0,0,0,40-40V184h56a24,24,0,0,0,23.82-27ZM72,144H32V56H72Zm150,21.29a7.88,7.88,0,0,1-6,2.71H152a8,8,0,0,0-8,8v24a24,24,0,0,1-19.29,23.54L88,150.11V56H204a8,8,0,0,1,7.94,7l12,96A7.87,7.87,0,0,1,222,165.29Z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        responseBox.appendChild(feedbackContainer);
                    }
                });

                // If there's a pending request for this session, add the loading indicator
                if (pendingRequests.has(currentSessionId)) {
                    const { loadingDiv } = pendingRequests.get(currentSessionId);
                    responseBox.appendChild(loadingDiv);
                }

                // Process markdown content after rendering
                setTimeout(() => processMarkdownContent(), 100);
            }

            // Use the robust scroll function
            scrollToBottom(responseBox);

            // Always ensure persona selector state is correct
            setPersonaSelectorState();
        }

        function submitQuickFeedback(type, messageIndex) {
            if (!currentSessionId) return;

            const currentChat = chatSessions[currentSessionId];
            if (!currentChat) return;

            if (!currentChat.feedback) {
                currentChat.feedback = [];
            }

            const feedback = {
                content: `Message ${messageIndex + 1}: ${type}`,
                timestamp: new Date().toISOString()
            };

            currentChat.feedback.push(feedback);

            // Save to server
            saveChatToServer(currentSessionId).catch(console.error);

            // Show confirmation
            const button = event.target.closest('.w-20');
            const originalContent = button.innerHTML;
            button.innerHTML = '<div class="text-green-600 text-sm">✓ Sent</div>';
            setTimeout(() => {
                button.innerHTML = originalContent;
            }, 2000);
        }

        async function sendQuery() {
            const queryInput = document.getElementById('queryInput');
            const query = queryInput.value.trim();
            if (!query || !currentSessionId) return;

            // Clear input immediately after getting the value
            queryInput.value = '';

            // Hide send button since input is now empty
            toggleSendButton();

            const responseBox = document.getElementById('responseOutput');
            const session = chatSessions[currentSessionId];
            const thisSessionId = currentSessionId; // Capture the current session ID
            const persona = document.getElementById('personaSelect').value;

            // Clear placeholder if it exists
            if (responseBox.querySelector('.flex.items-center.justify-center.h-full')) {
                responseBox.innerHTML = '';
            }

            // Add user message to history and UI
            session.history.push({ type: 'user', content: query });

            // Create user message UI
            const userMessageContainer = document.createElement('div');
            userMessageContainer.className = 'message-wrapper flex items-start gap-3 p-4 justify-end';
            userMessageContainer.innerHTML = `
                <div class="flex flex-1 flex-col gap-1 items-end">
                    <div class="user-message-content text-base font-normal leading-normal rounded-xl px-4 py-3" style="background-color: var(--chat-bg-user); color: var(--chat-text-user);">${query}</div>
                </div>
                <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 h-10 shrink-0 overflow-hidden user-avatar flex items-center justify-center" style="background-color: #e5e7eb;">
                </div>
            `;
            responseBox.appendChild(userMessageContainer);

            // Generate and insert user identicon
            const userId = getUserId();
            const currentAvatarContainer = userMessageContainer.querySelector('.user-avatar');
            if (currentAvatarContainer) {
                currentAvatarContainer.innerHTML = getUserAvatar(userId);
            }

            // Use the robust scroll function
            scrollToBottom(responseBox, userMessageContainer);

            // Update chat title with first query
            if (session.history.filter(m => m.type === 'user').length === 1) {
                chatSessions[thisSessionId].title = query.substring(0, 30) + (query.length > 30 ? '...' : '');
                renderChatList();
            }
            await saveChatToServer(thisSessionId);

            setPersonaSelectorState(); // Disable persona selector after first user message

            // Create simple loading indicator
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'mx-auto max-w-[960px] py-2';
            loadingDiv.setAttribute('data-session-id', thisSessionId);
            loadingDiv.innerHTML = `
                <div class="flex items-center gap-3 text-sm px-4" style="color: var(--text-secondary);">
                    <div class="animate-spin rounded-full h-3 w-3 border border-transparent border-t-current"></div>
                    <span id="task-summary-inline" class="text-xs opacity-60">Starting...</span>
                    <div class="flex-1"></div>
                </div>

                <!-- Simple progress bar -->
                <div id="task-progress-bar" class="hidden mx-4 mt-1">
                    <div class="w-full bg-gray-200 rounded-full h-0.5" style="background-color: var(--bg-secondary);">
                        <div id="task-progress-fill" class="h-0.5 rounded-full transition-all duration-500" style="background-color: var(--accent-blue); width: 0%;"></div>
                    </div>
                </div>

                <!-- Current task -->
                <div id="task-current" class="hidden mx-4 mt-1 text-xs" style="color: var(--text-secondary);">
                    <span id="task-current-text"></span>
                </div>
            `;

            // Add to pending requests and append to UI
            pendingRequests.set(thisSessionId, {
                loadingDiv: loadingDiv,
                startTime: Date.now()
            });

            responseBox.appendChild(loadingDiv);

            // Start task polling for this session
            startTaskPolling(thisSessionId);

            // Auto-scroll to bottom
            responseBox.scrollTop = responseBox.scrollHeight;

            try {
                // Fetch insights
                const response = await fetch('/ask', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        query: query,
                        session_id: thisSessionId,
                        persona: persona,
                        user_id: userId
                    })
                });
                                const data = await response.json();

                // Clean up loading state
                pendingRequests.delete(thisSessionId);

                // Stop task polling
                stopTaskPolling();

                const botResponse = data.response || 'An error occurred. Please try again.';

                // Process task execution summary from response
                let finalTaskDisplay = '';
                let taskExecutionSummary = null;

                if (data.task_execution_summary && data.task_execution_summary.tasks && data.task_execution_summary.tasks.length > 0) {
                    taskExecutionSummary = data.task_execution_summary;

                    // Render task display from response data
                    const statusEmojis = {
                        'pending': '⏳',
                        'in_progress': '🔄',
                        'completed': '✅',
                        'failed': '❌',
                        'cancelled': '🚫'
                    };

                    let allTasksHTML = '';
                    data.task_execution_summary.tasks.forEach(task => {
                        const emoji = statusEmojis[task.status] || '❓';
                        const description = task.description.length > 150
                            ? task.description.substring(0, 150) + '...'
                            : task.description;

                        allTasksHTML += `<div class="flex items-start gap-2 text-xs mb-1" style="color: var(--text-secondary);">
                            <span class="text-sm">${emoji}</span>
                            <span class="flex-1 markdown-content">${renderMarkdown(description)}</span>
                        </div>`;
                    });

                    if (allTasksHTML) {
                        finalTaskDisplay = `<div class="mx-4 mt-1 text-xs" style="color: var(--text-secondary);">
                            <span>${allTasksHTML}</span>
                        </div>`;
                    }

                    // Archive tasks now that we have the final state
                    try {
                        await fetch(`/sessions/${thisSessionId}/tasks/archive`, {
                            method: 'POST',
                            headers: {'Content-Type': 'application/json'}
                        });
                    } catch (e) {
                        console.warn('Failed to archive tasks:', e);
                    }
                }

                // Add bot message to history with task summary
                const botMessage = {
                    type: 'bot',
                    content: botResponse,
                    task_execution_summary: taskExecutionSummary
                };
                chatSessions[thisSessionId].history.push(botMessage);
                chatSessions[thisSessionId].persona = persona;
                await saveChatToServer(thisSessionId);

                // Only update UI if this is still the current session
                if (thisSessionId === currentSessionId) {
                    responseBox.removeChild(loadingDiv);

                    // Create bot message
                    const botMessageContainer = document.createElement('div');
                    botMessageContainer.className = 'message-wrapper bot-message-container';
                    botMessageContainer.innerHTML = `
                        <div class="flex items-start gap-3 p-4 w-full">
                            <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 h-10 shrink-0 bg-blue-500 flex items-center justify-center">
                                <span class="text-white font-semibold">A</span>
                            </div>
                            <div class="flex flex-col gap-1 flex-1 min-w-0 w-full">
                                ${finalTaskDisplay ? `
                                <div class="mb-3 p-3 rounded-lg border" style="background-color: var(--bg-secondary); border-color: var(--border-color);">
                                    <div class="text-xs font-medium mb-2" style="color: var(--text-primary);">Task Execution Summary</div>
                                    ${finalTaskDisplay}
                                </div>` : ''}
                                <div class="bot-message-content markdown-content text-base font-normal leading-normal rounded-xl px-4 py-3" style="background-color: var(--chat-bg-bot); color: var(--chat-text-bot);">${renderMarkdown(botResponse)}</div>
                            </div>
                        </div>
                    `;
                    responseBox.appendChild(botMessageContainer);

                    // Add feedback buttons
                    const feedbackContainer = document.createElement('div');
                    feedbackContainer.className = '@container ml-14';
                    const messageIndex = session.history.length - 1;
                    feedbackContainer.innerHTML = `
                        <div class="gap-2 px-4 flex flex-wrap justify-start">
                            <div class="flex flex-col items-center gap-2 py-2.5 text-center w-20 cursor-pointer transition-colors" style="background-color: var(--bg-primary);" onmouseover="this.style.backgroundColor=getComputedStyle(document.documentElement).getPropertyValue('--hover-bg')" onmouseout="this.style.backgroundColor=getComputedStyle(document.documentElement).getPropertyValue('--bg-primary')" onclick="submitQuickFeedback('helpful', ${messageIndex})">
                                <div class="rounded-full p-2.5" style="background-color: var(--bg-secondary);">
                                    <div style="color: var(--text-primary);" data-icon="ThumbsUp" data-size="20px" data-weight="regular">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                                            <path d="M234,80.12A24,24,0,0,0,216,72H160V56a40,40,0,0,0-40-40,8,8,0,0,0-7.16,4.42L75.06,96H32a16,16,0,0,0-16,16v88a16,16,0,0,0,16,16H204a24,24,0,0,0,23.82-21l12-96A24,24,0,0,0,234,80.12ZM32,112H72v88H32ZM223.94,97l-12,96a8,8,0,0,1-7.94,7H88V105.89l36.71-73.43A24,24,0,0,1,144,56V80a8,8,0,0,0,8,8h64a8,8,0,0,1,7.94,9Z"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-col items-center gap-2 py-2.5 text-center w-20 cursor-pointer transition-colors" style="background-color: var(--bg-primary);" onmouseover="this.style.backgroundColor=getComputedStyle(document.documentElement).getPropertyValue('--hover-bg')" onmouseout="this.style.backgroundColor=getComputedStyle(document.documentElement).getPropertyValue('--bg-primary')" onclick="submitQuickFeedback('not helpful', ${messageIndex})">
                                <div class="rounded-full p-2.5" style="background-color: var(--bg-secondary);">
                                    <div style="color: var(--text-primary);" data-icon="ThumbsDown" data-size="20px" data-weight="regular">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                                            <path d="M239.82,157l-12-96A24,24,0,0,0,204,40H32A16,16,0,0,0,16,56v88a16,16,0,0,0,16,16H75.06l37.78,75.58A8,8,0,0,0,120,240a40,40,0,0,0,40-40V184h56a24,24,0,0,0,23.82-27ZM72,144H32V56H72Zm150,21.29a7.88,7.88,0,0,1-6,2.71H152a8,8,0,0,0-8,8v24a24,24,0,0,1-19.29,23.54L88,150.11V56H204a8,8,0,0,1,7.94,7l12,96A7.87,7.87,0,0,1,222,165.29Z"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    responseBox.appendChild(feedbackContainer);

                    // Process markdown content for the new message
                    setTimeout(() => processMarkdownContent(), 100);

                    // Use the robust scroll function
                    scrollToBottom(responseBox, feedbackContainer);
                } else {
                    // If we've switched sessions, just re-render the chat that got the response
                    renderCurrentChat();
                }
                        } catch (error) {
                // Clean up loading state on error
                pendingRequests.delete(thisSessionId);

                // Stop task polling
                stopTaskPolling();

                // Get final task status before cleanup (for error case)
                let finalTaskDisplay = '';
                let taskExecutionSummary = null;
                try {
                    // Fetch final task status to ensure we have the latest data
                    const finalTaskData = await fetchTaskStatus(thisSessionId);
                    if (finalTaskData && finalTaskData.tasks && finalTaskData.tasks.length > 0) {
                        taskExecutionSummary = {
                            tasks: finalTaskData.tasks,
                            summary: finalTaskData.summary,
                            completed_at: new Date().toISOString()
                        };

                        // Manually render the final task display
                        const statusEmojis = {
                            'pending': '⏳',
                            'in_progress': '🔄',
                            'completed': '✅',
                            'failed': '❌',
                            'cancelled': '🚫'
                        };

                        let allTasksHTML = '';
                        finalTaskData.tasks.forEach(task => {
                            const emoji = statusEmojis[task.status] || '❓';
                            const description = task.description.length > 150
                                ? task.description.substring(0, 150) + '...'
                                : task.description;

                            allTasksHTML += `<div class="flex items-start gap-2 text-xs mb-1" style="color: var(--text-secondary);">
                                <span class="text-sm">${emoji}</span>
                                <span class="flex-1 markdown-content">${renderMarkdown(description)}</span>
                            </div>`;
                        });

                        if (allTasksHTML) {
                            finalTaskDisplay = `<div class="mx-4 mt-1 text-xs" style="color: var(--text-secondary);">
                                <span>${allTasksHTML}</span>
                            </div>`;
                        }

                        // Archive tasks on error too
                        try {
                            await fetch(`/sessions/${thisSessionId}/tasks/archive`, {
                                method: 'POST',
                                headers: {'Content-Type': 'application/json'}
                            });
                        } catch (e) {
                            console.warn('Failed to archive tasks on error:', e);
                        }
                    }
                } catch (e) {
                    // Fallback to existing task display if fetch fails
                    const currentTaskElement = loadingDiv.querySelector('#task-current');
                    if (currentTaskElement && !currentTaskElement.classList.contains('hidden')) {
                        finalTaskDisplay = currentTaskElement.outerHTML;
                    }
                }

                // Add error message to history with task summary
                const errorMessage = 'An error occurred while fetching the response. Please try again.';
                const errorBotMessage = {
                    type: 'bot',
                    content: errorMessage,
                    task_execution_summary: taskExecutionSummary
                };
                chatSessions[thisSessionId].history.push(errorBotMessage);
                await saveChatToServer(thisSessionId);

                // Update UI based on current session
                if (thisSessionId === currentSessionId) {
                    responseBox.removeChild(loadingDiv);
                    const errorContainer = document.createElement('div');
                    errorContainer.className = 'message-wrapper bot-message-container';
                    errorContainer.innerHTML = `
                        <div class="flex items-start gap-3 p-4 w-full">
                            <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 h-10 shrink-0 bg-red-500 flex items-center justify-center">
                                <span class="text-white font-semibold">!</span>
                            </div>
                            <div class="flex flex-col gap-1 flex-1 min-w-0 w-full">
                                ${finalTaskDisplay ? `
                                <div class="mb-3 p-3 rounded-lg border" style="background-color: var(--bg-secondary); border-color: var(--border-color);">
                                    <div class="text-xs font-medium mb-2" style="color: var(--text-primary);">Task Execution Summary (Interrupted)</div>
                                    ${finalTaskDisplay}
                                </div>` : ''}
                                <p class="text-[13px] font-normal leading-normal" style="color: var(--text-secondary);">Error</p>
                                <div class="bot-message-content markdown-content text-base font-normal leading-normal rounded-xl px-4 py-3 bg-red-100 text-red-800">${renderMarkdown(errorMessage)}</div>
                            </div>
                        </div>
                    `;
                    responseBox.appendChild(errorContainer);

                    // Use the robust scroll function
                    scrollToBottom(responseBox, errorContainer);
                } else {
                    renderCurrentChat();
                }
            }
        }

        // Session ID Display Functions
        function updateSessionIdDisplay() {
            const display = document.getElementById('sessionIdDisplay');
            const sessionIdText = document.getElementById('sessionIdText');

            if (currentSessionId) {
                sessionIdText.textContent = currentSessionId;
                display.style.display = 'flex';
            } else {
                display.style.display = 'none';
            }
        }

        function copySessionId() {
            if (!currentSessionId) return;

            const copyBtn = document.getElementById('copySessionIdBtn');
            const originalContent = copyBtn.innerHTML;

            // Copy to clipboard
            navigator.clipboard.writeText(currentSessionId).then(() => {
                // Visual feedback
                copyBtn.innerHTML = `
                    <svg class="w-3 h-3" style="color: var(--accent-blue);" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                `;
                copyBtn.title = 'Copied!';

                // Reset after 2 seconds
                setTimeout(() => {
                    copyBtn.innerHTML = originalContent;
                    copyBtn.title = 'Copy Session ID';
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy session ID:', err);
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = currentSessionId;
                document.body.appendChild(textArea);
                textArea.select();
                try {
                    document.execCommand('copy');
                    copyBtn.innerHTML = `
                        <svg class="w-3 h-3" style="color: var(--accent-blue);" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    `;
                    copyBtn.title = 'Copied!';
                    setTimeout(() => {
                        copyBtn.innerHTML = originalContent;
                        copyBtn.title = 'Copy Session ID';
                    }, 2000);
                } catch (fallbackErr) {
                    console.error('Fallback copy failed:', fallbackErr);
                }
                document.body.removeChild(textArea);
            });
        }


    </script>
</body>
</html>
