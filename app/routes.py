import base64
import datetime
import json
import logging
import threading
from concurrent.futures import ThreadPoolExecutor

from dateutil.parser import isoparse
from flask import jsonify, render_template, request
from google.genai import types

from src.client_managers import get_genai_manager
from src.env_config import EnvConfig
from src.logging_utils import LoggingUtils
from src.prompt_manager import PromptManager
from src.retry_utils import call_google_genai_with_retry

from .utils.session_cache import get_cache_stats, invalidate_session_cache


def get_agent():
    """Get the LogIntelligenceAgentLangGraph instance from Flask app context."""
    from flask import current_app

    if not hasattr(current_app, "agent") or current_app.agent is None:
        raise RuntimeError(
            "LogIntelligenceAgentLangGraph not properly initialized in application context. "
            "This indicates an application startup issue."
        )

    return current_app.agent


def get_db_manager():
    """Get the DatabaseManager instance from Flask app context."""
    from flask import current_app

    if not hasattr(current_app, "db_manager") or current_app.db_manager is None:
        raise RuntimeError(
            "DatabaseManager not properly initialized in application context. "
            "This indicates an application startup issue."
        )

    return current_app.db_manager


from .utils.markdown_utils import render_markdown

# Initialize config - will be consistent with client managers
config = EnvConfig.load()


def to_bson_datetime(timestamp):
    """
    Converts a timestamp (ISO 8601 string, epoch seconds, epoch ms, or datetime object)
    into a BSON-compatible datetime (Python datetime).
    Returns None if input is None or invalid.
    """

    def _from_numeric(num):
        try:
            if num > 1e12:  # nanoseconds, not supported
                return None
            elif num > 1e10:  # milliseconds
                return datetime.datetime.fromtimestamp(
                    num / 1000, tz=datetime.timezone.utc
                )
            else:  # seconds
                return datetime.datetime.fromtimestamp(num, tz=datetime.timezone.utc)
        except Exception:
            return None

    def _from_string(s):
        try:
            # Try ISO 8601
            dt = isoparse(s)
            return dt
        except Exception:
            pass
        try:
            # Try as float/int epoch
            num = float(s)
            return _from_numeric(num)
        except Exception:
            return None

    if timestamp is None:
        return None

    if isinstance(timestamp, datetime.datetime):
        return timestamp

    if isinstance(timestamp, (int, float)):
        return _from_numeric(timestamp)

    if isinstance(timestamp, str):
        return _from_string(timestamp)

    return None


class Chat:
    def __init__(self, id):
        self.id = id
        self.messages = []
        self.feedback = []
        self.persona = "business"

    def to_dict(self):
        return {
            "id": self.id,
            "messages": self.messages,
            "feedback": self.feedback,
            "persona": self.persona,
        }

    @staticmethod
    def from_dict(data):
        chat = Chat(data["id"])
        chat.messages = data["messages"]
        chat.feedback = data.get("feedback", [])  # Handle legacy chats without feedback
        chat.persona = data.get("persona", "business")  # Default to business persona
        return chat


def init_routes(app):
    @app.route("/")
    def index():
        logo_b64 = "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"
        return render_template("index.html", logo_b64=logo_b64)

    @app.route("/ask", methods=["POST"])
    def ask():
        data = request.json
        query = data.get("query")
        session_id = data.get("session_id")
        persona = data.get("persona", "business")  # Default to business persona
        user_id = data.get("user_id")

        if not query or not session_id:
            return jsonify({"response": ""})

        # Use the intelligent agent to get the answer
        # Note: User message storage is now handled inside agent.run() with proper run_id
        agent = get_agent()  # Use the same agent instance as other endpoints

        # Debug logging
        logging.info(f"Processing query for session {session_id}")
        logging.info(f"Agent instance: {id(agent)}")

        answer = agent.run(
            question=query, session_id=session_id, persona=persona, user_id=user_id
        )

        # Process markdown for HTML display
        answer_html = render_markdown(answer, include_css=False)

        # Debug logging after processing
        logging.info(f"LangGraph agent processing completed for session {session_id}")

        # Return response (LangGraph agent doesn't use task managers)
        return jsonify({"response": answer_html})

    @app.route("/sessions", methods=["GET"])
    def list_sessions():
        """List all chat sessions with pagination and filtering."""
        try:
            db_manager = get_db_manager()

            # Get query parameters
            page = int(request.args.get("page", 1))
            limit = int(request.args.get("limit", 10))
            status = request.args.get("status")  # active, completed, archived
            user_id = request.args.get("user_id")
            persona = request.args.get("persona")

            # Build MongoDB filter
            filter_query = {}
            if status:
                filter_query["status"] = status
            if user_id:
                filter_query["user_id"] = user_id
            if persona:
                filter_query["persona"] = persona

            # If no user_id filter and no other filters, exclude archived by default
            if not user_id and not status and not persona:
                filter_query["status"] = {"$ne": "archived"}

            # Calculate skip value for pagination
            skip = (page - 1) * limit

            # Query sessions from MongoDB
            sessions_raw = db_manager.query_mongodb(
                collection="chat_sessions",
                query_type="find",
                filter=filter_query,
                sort={"last_activity": -1},  # Most recent first
                limit=limit,
                skip=skip,
            )

            # Populate each session with its complete chat data (including messages)
            sessions = []
            for session in sessions_raw:
                session_id = session.get("session_id")
                if session_id:
                    try:
                        # Get complete session data including messages
                        complete_session = db_manager.get_chat_session(session_id)
                        if complete_session:
                            sessions.append(complete_session)
                        else:
                            # Fallback to basic session info if no messages
                            # Ensure basic session has required fields for UI compatibility
                            basic_session = dict(session)
                            basic_session["history"] = []
                            basic_session["feedback"] = []
                            basic_session["archived"] = (
                                basic_session.get("status") == "archived"
                            )
                            sessions.append(basic_session)
                    except Exception:
                        logging.exception(f"Error loading session {session_id}")
                        # Still add basic session info to avoid losing data
                        basic_session = dict(session)
                        basic_session["history"] = []
                        basic_session["feedback"] = []
                        basic_session["archived"] = (
                            basic_session.get("status") == "archived"
                        )
                        sessions.append(basic_session)

            # Get total count for pagination
            total_count = db_manager.database.chat_sessions.count_documents(
                filter_query
            )

            return jsonify(
                {
                    "sessions": sessions,
                    "pagination": {
                        "page": page,
                        "limit": limit,
                        "total": total_count,
                        "pages": (total_count + limit - 1) // limit,
                    },
                    "filters": {
                        "status": status,
                        "user_id": user_id,
                        "persona": persona,
                    },
                }
            )

        except Exception:
            logging.exception(f"Error listing sessions")
            return jsonify({"error": "Failed to retrieve sessions"}), 500

    @app.route("/sessions/stats", methods=["GET"])
    def get_session_stats():
        """Get overall session statistics."""
        try:
            db_manager = get_db_manager()

            # Aggregate session statistics
            pipeline = [
                {
                    "$group": {
                        "_id": None,
                        "total_sessions": {"$sum": 1},
                        "active_sessions": {
                            "$sum": {"$cond": [{"$eq": ["$status", "active"]}, 1, 0]}
                        },
                        "completed_sessions": {
                            "$sum": {"$cond": [{"$eq": ["$status", "completed"]}, 1, 0]}
                        },
                        "total_messages": {"$sum": "$message_count"},
                        "avg_messages_per_session": {"$avg": "$message_count"},
                    }
                }
            ]

            stats = db_manager.query_mongodb(
                collection="chat_sessions", query_type="aggregate", pipeline=pipeline
            )

            # Get persona distribution
            persona_pipeline = [{"$group": {"_id": "$persona", "count": {"$sum": 1}}}]

            persona_stats = db_manager.query_mongodb(
                collection="chat_sessions",
                query_type="aggregate",
                pipeline=persona_pipeline,
            )

            result = {
                "overall": stats[0] if stats else {},
                "by_persona": {item["_id"]: item["count"] for item in persona_stats},
            }

            return jsonify(result)

        except Exception:
            logging.exception(f"Error getting session stats")
            return jsonify({"error": "Failed to retrieve session statistics"}), 500

    @app.route("/sessions/<session_id>/logs", methods=["GET"])
    def get_session_logs_only(session_id):
        """Get only the logs for a specific session with filtering options."""
        try:
            db_manager = get_db_manager()

            # Get query parameters
            limit = int(request.args.get("limit", 100))
            log_types = request.args.get("log_types")
            if log_types:
                log_types = log_types.split(",")

            # Get logs from MongoDB
            logs = db_manager.get_session_logs(
                session_id, limit=limit, log_types=log_types
            )

            # If no MongoDB logs, try audit trail as fallback
            if not logs:
                logs = _get_session_logs_from_audit_trail(session_id)
                source = "audit_trail_fallback"
            else:
                source = "mongodb"

            return jsonify(
                {
                    "session_id": session_id,
                    "logs": logs,
                    "count": len(logs),
                    "source": source,
                    "filters": {"limit": limit, "log_types": log_types},
                }
            )

        except Exception:
            logging.exception(f"Error retrieving logs for session {session_id}")
            return jsonify({"error": "Failed to retrieve session logs"}), 500

    @app.route("/sessions/logs/stats", methods=["GET"])
    def get_logs_stats():
        """Get aggregate statistics about session logs."""
        try:
            db_manager = get_db_manager()

            # Aggregate log statistics
            pipeline = [
                {
                    "$group": {
                        "_id": None,
                        "total_logs": {"$sum": 1},
                        "total_sessions_with_logs": {"$addToSet": "$session_id"},
                        "by_level": {"$push": "$log_level"},
                        "by_type": {"$push": "$message_type"},
                    }
                },
                {
                    "$project": {
                        "total_logs": 1,
                        "total_sessions_with_logs": {
                            "$size": "$total_sessions_with_logs"
                        },
                        "by_level": 1,
                        "by_type": 1,
                    }
                },
            ]

            stats = db_manager.query_mongodb(
                collection="session_logs", query_type="aggregate", pipeline=pipeline
            )

            # Process level and type counts
            result = {"logs": {}}
            if stats:
                stat = stats[0]
                result["logs"] = {
                    "total_logs": stat.get("total_logs", 0),
                    "sessions_with_logs": stat.get("total_sessions_with_logs", 0),
                }

                # Count by level
                level_counts = {}
                for level in stat.get("by_level", []):
                    level_counts[level] = level_counts.get(level, 0) + 1
                result["logs"]["by_level"] = level_counts

                # Count by type
                type_counts = {}
                for msg_type in stat.get("by_type", []):
                    type_counts[msg_type] = type_counts.get(msg_type, 0) + 1
                result["logs"]["by_type"] = type_counts

            return jsonify(result)

        except Exception:
            logging.exception(f"Error getting logs stats")
            return jsonify({"error": "Failed to retrieve logs statistics"}), 500

    @app.route("/sessions/<session_id>/runs", methods=["GET"])
    def get_session_runs(session_id):
        """Get all run_ids for a session."""
        try:
            db_manager = get_db_manager()
            run_ids = db_manager.get_session_run_ids(session_id)

            return jsonify(
                {
                    "session_id": session_id,
                    "run_ids": run_ids,
                    "total_runs": len(run_ids),
                }
            )

        except Exception:
            logging.exception(f"Error getting runs for session {session_id}")
            return jsonify({"error": "Failed to retrieve session runs"}), 500

    @app.route("/sessions/<session_id>/run/<run_id>", methods=["GET"])
    def get_session_run_details(session_id, run_id):
        """Get detailed data for a specific run within a session."""
        try:
            db_manager = get_db_manager()
            run_data = db_manager.get_session_by_run_id(session_id, run_id)

            if not run_data:
                return jsonify({"error": "Run not found"}), 404

            return jsonify(run_data)

        except Exception:
            logging.exception(f"Error getting run {run_id} for session {session_id}")
            return jsonify({"error": "Failed to retrieve run details"}), 500

    @app.route("/sessions/<session_id>/detailed", methods=["GET"])
    def get_session_with_run_breakdown(session_id):
        """Get complete session data organized by run_id."""
        try:
            db_manager = get_db_manager()
            session_data = db_manager.get_session_with_run_details(session_id)

            if not session_data:
                return jsonify({"error": "Session not found"}), 404

            return jsonify(session_data)

        except Exception:
            logging.exception(f"Error getting detailed session {session_id}")
            return jsonify({"error": "Failed to retrieve detailed session data"}), 500

    @app.route("/users/<user_id>/sessions", methods=["GET"])
    def get_user_sessions(user_id):
        """Get all sessions for a specific user with detailed statistics."""
        try:
            db_manager = get_db_manager()

            # Get query parameters
            page = int(request.args.get("page", 1))
            limit = int(request.args.get("limit", 20))
            status = request.args.get("status")

            # Build filter
            filter_query = {"user_id": user_id}
            if status:
                filter_query["status"] = status

            # Calculate skip for pagination
            skip = (page - 1) * limit

            # Get user sessions
            sessions = db_manager.query_mongodb(
                collection="chat_sessions",
                query_type="find",
                filter=filter_query,
                sort={"last_activity": -1},
                limit=limit,
                skip=skip,
            )

            # Get total count
            total_count = db_manager.database.chat_sessions.count_documents(
                filter_query
            )

            # Get user activity statistics
            stats_pipeline = [
                {"$match": {"user_id": user_id}},
                {
                    "$group": {
                        "_id": "$user_id",
                        "total_sessions": {"$sum": 1},
                        "total_messages": {"$sum": "$message_count"},
                        "active_sessions": {
                            "$sum": {"$cond": [{"$eq": ["$status", "active"]}, 1, 0]}
                        },
                        "completed_sessions": {
                            "$sum": {"$cond": [{"$eq": ["$status", "completed"]}, 1, 0]}
                        },
                        "first_session": {"$min": "$created_at"},
                        "last_activity": {"$max": "$last_activity"},
                        "avg_messages_per_session": {"$avg": "$message_count"},
                    }
                },
            ]

            user_stats = db_manager.query_mongodb(
                collection="chat_sessions",
                query_type="aggregate",
                pipeline=stats_pipeline,
            )

            return jsonify(
                {
                    "user_id": user_id,
                    "sessions": sessions,
                    "pagination": {
                        "page": page,
                        "limit": limit,
                        "total": total_count,
                        "pages": (total_count + limit - 1) // limit,
                    },
                    "user_statistics": user_stats[0] if user_stats else {},
                    "filters": {"status": status},
                }
            )

        except Exception:
            logging.exception(f"Error getting sessions for user {user_id}")
            return jsonify({"error": "Failed to retrieve user sessions"}), 500

    @app.route("/users/stats", methods=["GET"])
    def get_users_overview():
        """Get overview of all users and their activity."""
        try:
            db_manager = get_db_manager()

            # Aggregate user statistics
            pipeline = [
                {
                    "$group": {
                        "_id": "$user_id",
                        "session_count": {"$sum": 1},
                        "total_messages": {"$sum": "$message_count"},
                        "last_activity": {"$max": "$last_activity"},
                        "first_session": {"$min": "$created_at"},
                        "active_sessions": {
                            "$sum": {"$cond": [{"$eq": ["$status", "active"]}, 1, 0]}
                        },
                    }
                },
                {"$sort": {"session_count": -1}},
                {"$limit": 50},  # Top 50 most active users
            ]

            user_stats = db_manager.query_mongodb(
                collection="chat_sessions", query_type="aggregate", pipeline=pipeline
            )

            # Overall statistics
            overall_pipeline = [
                {
                    "$group": {
                        "_id": None,
                        "total_users": {"$addToSet": "$user_id"},
                        "total_sessions": {"$sum": 1},
                        "total_messages": {"$sum": "$message_count"},
                    }
                },
                {
                    "$project": {
                        "total_users": {"$size": "$total_users"},
                        "total_sessions": 1,
                        "total_messages": 1,
                    }
                },
            ]

            overall_stats = db_manager.query_mongodb(
                collection="chat_sessions",
                query_type="aggregate",
                pipeline=overall_pipeline,
            )

            return jsonify(
                {
                    "overview": overall_stats[0] if overall_stats else {},
                    "top_users": user_stats,
                }
            )

        except Exception:
            logging.exception(f"Error getting users overview")
            return jsonify({"error": "Failed to retrieve users overview"}), 500

    @app.route("/sessions/<session_id>/archive", methods=["POST"])
    def archive_session(session_id):
        """Archive a session (soft delete)."""
        try:
            db_manager = get_db_manager()

            # Invalidate cache for this session since it's being archived
            invalidate_session_cache(session_id)

            # Archive the session in MongoDB
            archived = db_manager.archive_chat_session(session_id)

            if archived:
                return jsonify(
                    {
                        "status": "success",
                        "message": f"Session {session_id} archived successfully",
                        "session_id": session_id,
                    }
                )
            else:
                return (
                    jsonify(
                        {
                            "status": "not_found",
                            "message": f"Session {session_id} not found",
                        }
                    ),
                    404,
                )

        except Exception:
            logging.exception(f"Error archiving session {session_id}")
            return jsonify({"error": "Failed to archive session"}), 500

    @app.route("/sessions/<session_id>/restore", methods=["POST"])
    def restore_session(session_id):
        """Restore an archived session to active status."""
        try:
            db_manager = get_db_manager()
            restored = db_manager.restore_chat_session(session_id)

            if restored:
                return jsonify(
                    {
                        "status": "success",
                        "message": f"Session {session_id} restored successfully",
                        "session_id": session_id,
                    }
                )
            else:
                return (
                    jsonify(
                        {
                            "status": "not_found",
                            "message": f"Archived session {session_id} not found",
                        }
                    ),
                    404,
                )

        except Exception:
            logging.exception(f"Error restoring session {session_id}")
            return jsonify({"error": "Failed to restore session"}), 500

    @app.route("/sessions/archived", methods=["GET"])
    def get_archived_sessions():
        """Get all archived sessions with pagination."""
        try:
            db_manager = get_db_manager()

            # Get query parameters
            page = int(request.args.get("page", 1))
            limit = int(request.args.get("limit", 20))
            user_id = request.args.get("user_id")

            # Build filter for archived sessions
            filter_query = {"status": "archived"}
            if user_id:
                filter_query["user_id"] = user_id

            # Calculate skip for pagination
            skip = (page - 1) * limit

            # Get archived sessions
            sessions = db_manager.query_mongodb(
                collection="chat_sessions",
                query_type="find",
                filter=filter_query,
                sort={"archived_at": -1},  # Most recently archived first
                limit=limit,
                skip=skip,
            )

            # Get total count
            total_count = db_manager.database.chat_sessions.count_documents(
                filter_query
            )

            return jsonify(
                {
                    "archived_sessions": sessions,
                    "pagination": {
                        "page": page,
                        "limit": limit,
                        "total": total_count,
                        "pages": (total_count + limit - 1) // limit,
                    },
                    "filters": {"user_id": user_id},
                }
            )

        except Exception:
            logging.exception(f"Error getting archived sessions")
            return jsonify({"error": "Failed to retrieve archived sessions"}), 500

    def _infer_chat_data_from_logs(session_id, logs):
        """
        Infer chat data structure from agent logs when chat file is not available.
        """
        # Extract basic information from logs
        title = f"Session {session_id[:8]}..."
        persona = "unknown"
        questions = []

        for log in logs:
            if isinstance(log.get("details"), dict):
                details = log["details"]

                # Extract persona
                if details.get("persona"):
                    persona = details["persona"]

                # Extract question
                question = details.get("question")
                if question and question not in questions:
                    questions.append(question)

                # Update title based on first question
                if len(questions) == 1:
                    title = question[:50] + "..." if len(question) > 50 else question

        # Create minimal chat structure
        history = []
        for i, question in enumerate(questions):
            history.append({"type": "user", "content": question})
            # Add placeholder for bot response
            history.append(
                {
                    "type": "bot",
                    "content": f"[Response inferred from logs - session {i+1}]",
                }
            )

        return {
            "title": title,
            "history": history,
            "persona": persona,
            "feedback": [],
            "archived": False,
            "user_id": None,
            "inferred_from_logs": True,
        }

    def _process_log_data(log_data):
        """
        Helper function to process log data and prepare it for storage.
        Returns a list of logs ready to be stored.
        """
        logs = log_data.get("logs")
        top_level_timestamp = log_data.get("timestamp")
        logs_to_store = []

        if isinstance(logs, list):
            # Multiple logs: ensure each has a timestamp
            for entry in logs:
                entry_copy = dict(entry)  # avoid mutating input
                if not entry_copy.get("timestamp"):
                    entry_copy["timestamp"] = top_level_timestamp
                logs_to_store.append(entry_copy)
        else:
            # Single log: just use the input as a single log entry
            logs_to_store = [log_data]

        return logs_to_store

    def _store_log_and_get_id(log, db_manager, config):
        """
        Store a log in the original collection and return its ID.

        Args:
            log: Log dictionary to store
            db_manager: DatabaseManager instance
            config: Environment configuration

        Returns:
            ObjectId: The ID of the inserted log, or None if failed
        """
        try:
            events_collection = db_manager.database.get_collection(
                config.mongodb_collection
            )

            # Ensure timestamps are BSON-compatible
            log_copy = log.copy()
            log_timestamp = log_copy.get("timestamp")
            if log_timestamp:
                log_copy["timestamp"] = to_bson_datetime(log_timestamp)

            # Insert the log and get the result
            result = events_collection.insert_one(log_copy)
            logging.info(
                f"Inserted log to original collection with ID: {result.inserted_id}"
            )

            return result.inserted_id

        except Exception:
            logging.exception(f"Error storing log to original collection")
            return None

    def _get_genai_client():
        """
        Get Google GenAI client from client manager.
        """
        try:
            return get_genai_manager(config).get_client()
        except Exception:
            logging.exception(f"Error getting Google GenAI client from manager")
            return None

    def _summarize_log_with_ai(log, model_name=None):
        """
        Summarize a log using AI model via Google GenAI client (same pattern as intelligent_agent.py).

        Args:
            log: Log dictionary to summarize
            model_name: AI model to use for summarization

        Returns:
            dict: Log with added summary fields, or original log if summarization fails
        """
        try:
            # Get the GenAI client
            client = _get_genai_client()
            if not client:
                logging.warning(
                    "Google GenAI client not available, skipping log summarization"
                )
                return log

            # Use configurable model name, fallback to default if not provided
            if model_name is None:
                model_name = config.summarization_model
                logging.info(f"Using configured summarization model: {model_name}")

            # Get the summarization prompt
            prompt_manager = PromptManager()
            prompt_content = prompt_manager.get_log_summarization_prompt()

            # Create the full prompt with log details
            def json_converter(o):
                if isinstance(o, datetime.datetime):
                    return o.isoformat()
                return str(o)

            full_prompt = f"{prompt_content}\n\nLog Details:\n{json.dumps(log, default=json_converter)}"

            # Create user message content (same pattern as intelligent_agent.py)
            user_content = types.Content(
                role="user", parts=[types.Part(text=full_prompt)]
            )

            # Generate summary using the client with retry logic
            response = call_google_genai_with_retry(
                client=client, model=model_name, contents=[user_content]
            )

            # Log the API response with metrics (same as agent does)
            LoggingUtils.log_model_response(response, "background_log_summarization")

            if response.text:
                # Create log with summary
                log_with_summary = log.copy()
                log_with_summary["summary"] = response.text.strip()
                log_with_summary["summary_generated_at"] = (
                    datetime.datetime.utcnow().isoformat()
                )
                log_with_summary["summary_model"] = model_name
                log_with_summary["summary_source"] = "pubsub_realtime"

                logging.info(
                    f"Successfully generated summary for log: {len(response.text)} characters"
                )
                return log_with_summary
            else:
                logging.warning("No response text from AI model")
                return log

        except Exception:
            logging.exception(f"Error summarizing log with AI")
            # Return original log if summarization fails
            return log

    def _store_summarized_log(log_with_summary, db_manager, config):
        """
        Store a log with summary to the summarized collection.

        Args:
            log_with_summary: Log dictionary with summary
            db_manager: DatabaseManager instance
            config: Environment configuration
        """
        try:
            summarized_collection = db_manager.database.get_collection(
                config.mongodb_summarized_collection
            )

            # Ensure timestamps are BSON-compatible
            log_copy = log_with_summary.copy()
            log_timestamp = log_copy.get("timestamp")
            if log_timestamp:
                log_copy["timestamp"] = to_bson_datetime(log_timestamp)

            # Insert the summarized log
            summarized_collection.insert_one(log_copy)
            logging.info(
                f"Inserted summarized log to {config.mongodb_summarized_collection}"
            )

        except Exception:
            logging.exception(f"Error storing summarized log")
            # Don't raise - we don't want to fail the whole request if summarized storage fails

    def _store_summarized_log_and_get_id(log_with_summary, db_manager, config):
        """
        Store a log with summary to the summarized collection and return its ID.

        Args:
            log_with_summary: Log dictionary with summary
            db_manager: DatabaseManager instance
            config: Environment configuration

        Returns:
            ObjectId: The ID of the inserted summarized log, or None if failed
        """
        try:
            summarized_collection = db_manager.database.get_collection(
                config.mongodb_summarized_collection
            )

            # Ensure timestamps are BSON-compatible
            log_copy = log_with_summary.copy()
            log_timestamp = log_copy.get("timestamp")
            if log_timestamp:
                log_copy["timestamp"] = to_bson_datetime(log_timestamp)

            # Insert the summarized log and get the result
            result = summarized_collection.insert_one(log_copy)
            logging.info(
                f"Inserted summarized log to {config.mongodb_summarized_collection} with ID: {result.inserted_id}"
            )

            return result.inserted_id

        except Exception:
            logging.exception(f"Error storing summarized log")
            # Don't raise - we don't want to fail the whole request if summarized storage fails
            return None

    def _process_logs_with_summarization(logs_to_store, source_name="api"):
        """
        Process logs with AI summarization and return results.

        Args:
            logs_to_store: List of logs to process
            source_name: Name of the source for logging (e.g., "api", "pubsub", "prod")

        Returns:
            dict: Summarization results with log IDs
        """
        db_manager = get_db_manager()
        summarization_results = {
            "total_logs": len(logs_to_store),
            "summarized_successfully": 0,
            "summarization_errors": 0,
            "log_ids": {"original_collection": [], "summarized_collection": []},
        }

        for log in logs_to_store:
            try:
                # Store original log and capture its ID
                original_log_id = _store_log_and_get_id(log, db_manager, config)
                if original_log_id:
                    summarization_results["log_ids"]["original_collection"].append(
                        str(original_log_id)
                    )

                # Generate summary using AI
                log_with_summary = _summarize_log_with_ai(log)

                # Only store to summarized collection if we actually got a summary
                if "summary" in log_with_summary:
                    summarized_log_id = _store_summarized_log_and_get_id(
                        log_with_summary, db_manager, config
                    )
                    if summarized_log_id:
                        summarization_results["log_ids"][
                            "summarized_collection"
                        ].append(str(summarized_log_id))
                    summarization_results["summarized_successfully"] += 1
                else:
                    logging.error("Log processed but no summary generated")

            except Exception:
                logging.exception(f"Error in summarization pipeline for log")
                summarization_results["summarization_errors"] += 1
                # Continue processing other logs even if one fails

        # Log summarization results
        logging.info(f"{source_name} log processing complete: {summarization_results}")

        return summarization_results

    def _process_logs_with_summarization_async(logs_to_store, source_name="api"):
        """
        Process logs with AI summarization asynchronously and return results.

        Args:
            logs_to_store: List of logs to process
            source_name: Name of the source for logging (e.g., "api", "pubsub", "prod")

        Returns:
            dict: Summarization results with log IDs
        """
        db_manager = get_db_manager()
        summarization_results = {
            "total_logs": len(logs_to_store),
            "summarized_successfully": 0,
            "summarization_errors": 0,
            "log_ids": {"original_collection": [], "summarized_collection": []},
        }

        # Create a thread pool for async processing
        with ThreadPoolExecutor(max_workers=4) as executor:
            # Submit all log processing tasks
            future_to_log = {}
            for log in logs_to_store:
                future = executor.submit(
                    _process_single_log_async, log, db_manager, config
                )
                future_to_log[future] = log

            # Collect results as they complete
            for future in future_to_log:
                try:
                    result = future.result(timeout=300)  # 5 minute timeout per log
                    if result["original_log_id"]:
                        summarization_results["log_ids"]["original_collection"].append(
                            str(result["original_log_id"])
                        )
                    if result["summarized_log_id"]:
                        summarization_results["log_ids"][
                            "summarized_collection"
                        ].append(str(result["summarized_log_id"]))
                        summarization_results["summarized_successfully"] += 1
                except Exception:
                    logging.exception(f"Error processing log asynchronously")
                    summarization_results["summarization_errors"] += 1

        # Log summarization results
        logging.info(
            f"{source_name} async log processing complete: {summarization_results}"
        )

        return summarization_results

    def _process_single_log_async(log, db_manager, config):
        """
        Process a single log asynchronously (runs in thread pool).

        Args:
            log: Log dictionary to process
            db_manager: DatabaseManager instance
            config: Environment configuration

        Returns:
            dict: Results with log IDs
        """
        try:
            # Store original log and capture its ID
            original_log_id = _store_log_and_get_id(log, db_manager, config)

            # Generate summary using AI
            log_with_summary = _summarize_log_with_ai(log)

            # Only store to summarized collection if we actually got a summary
            summarized_log_id = None
            if "summary" in log_with_summary:
                summarized_log_id = _store_summarized_log_and_get_id(
                    log_with_summary, db_manager, config
                )

            return {
                "original_log_id": original_log_id,
                "summarized_log_id": summarized_log_id,
                "success": True,
            }

        except Exception as e:
            logging.exception(f"Error in async summarization pipeline for log")
            return {
                "original_log_id": None,
                "summarized_log_id": None,
                "success": False,
                "error": str(e),
            }

    def _store_logs_and_return_immediately(logs_to_store, source_name):
        """
        Store logs to database immediately and return response, then process summarization in background.

        Args:
            logs_to_store: List of logs to process
            source_name: Name of the source for logging

        Returns:
            dict: Immediate response with stored log IDs
        """
        db_manager = get_db_manager()
        stored_log_ids = []

        # Store all logs immediately and collect their IDs
        for log in logs_to_store:
            try:
                log_id = _store_log_and_get_id(log, db_manager, config)
                if log_id:
                    stored_log_ids.append(str(log_id))
                    # Add the _id to the original log dictionary for later use in summarization
                    log["_id"] = log_id
            except Exception:
                logging.exception(f"Error storing log immediately")
                # Continue with other logs even if one fails

        # Start background summarization thread
        from flask import current_app

        summarization_thread = threading.Thread(
            target=_background_summarization,
            args=(
                logs_to_store,
                source_name,
                current_app._get_current_object(),
            ),
            daemon=True,
        )
        summarization_thread.start()

        return {
            "status": "success",
            "message": f"Logs stored successfully, summarization in progress",
            "stored_log_ids": stored_log_ids,
            "total_logs": len(logs_to_store),
            "summarization_status": "background_processing",
        }

    def _background_summarization(logs_to_store, source_name, app):
        """
        Background thread function to handle summarization after logs are stored.

        Args:
            logs_to_store: List of logs to summarize
            source_name: Name of the source for logging
            app: Flask application instance for context
        """
        with app.app_context():
            try:
                logging.info(
                    f"Starting background summarization for {len(logs_to_store)} logs from {source_name}"
                )

                db_manager = get_db_manager()
                stored_log_ids = [log["_id"] for log in logs_to_store]
                summarization_results = {
                    "total_logs": len(logs_to_store),
                    "summarized_successfully": 0,
                    "summarization_errors": 0,
                    "log_ids": {
                        "original_collection": stored_log_ids,
                        "summarized_collection": [],
                    },
                }

                # Process summarization in thread pool
                with ThreadPoolExecutor(max_workers=4) as executor:
                    future_to_log = {}
                    for log in logs_to_store:
                        future = executor.submit(
                            _summarize_single_log_background, log, db_manager, config
                        )
                        future_to_log[future] = log

                    # Collect results as they complete
                    for future in future_to_log:
                        try:
                            result = future.result(
                                timeout=300
                            )  # 5 minute timeout per log
                            if result["summarized_log_id"]:
                                summarization_results["log_ids"][
                                    "summarized_collection"
                                ].append(str(result["summarized_log_id"]))
                                summarization_results["summarized_successfully"] += 1
                        except Exception:
                            logging.exception(f"Error in background summarization")
                            summarization_results["summarization_errors"] += 1

                logging.info(
                    f"Background summarization complete for {source_name}: {summarization_results}"
                )

            except Exception:
                logging.exception(f"Error in background summarization thread")

    def _summarize_single_log_background(log, db_manager, config):
        """
        Summarize a single log in background thread.

        Args:
            log: Log dictionary to summarize
            db_manager: DatabaseManager instance
            config: Environment configuration

        Returns:
            dict: Results with summarized log ID
        """
        try:
            # Generate summary using AI
            log_with_summary = _summarize_log_with_ai(log)

            # Only store to summarized collection if we actually got a summary
            summarized_log_id = None
            if "summary" in log_with_summary:
                summarized_log_id = _store_summarized_log_and_get_id(
                    log_with_summary, db_manager, config
                )

            return {"summarized_log_id": summarized_log_id, "success": True}

        except Exception as e:
            logging.exception(f"Error in background summarization for log")
            return {"summarized_log_id": None, "success": False, "error": str(e)}

    def _handle_log_ingestion(log_data, source_name, pubsub_message_id=None):
        """
        Unified handler for log ingestion with immediate response and background summarization.

        Args:
            log_data: The log data to process
            source_name: Name of the source for logging (e.g., "api", "pubsub")
            pubsub_message_id: Pub/Sub message ID for idempotency (if applicable)

        Returns:
            tuple: (response_data, status_code)
        """
        try:
            logs_to_store = _process_log_data(log_data)

            # Attach Pub/Sub message ID for idempotency if provided
            if pubsub_message_id:
                for log in logs_to_store:
                    log.setdefault("pubsub_message_id", pubsub_message_id)

            immediate_response = _store_logs_and_return_immediately(
                logs_to_store, source_name
            )

            # Customize response message for Pub/Sub
            if pubsub_message_id:
                response_data = {
                    "status": "success",
                    "message": "PubSub log event(s) stored successfully",
                    "stored_log_ids": immediate_response["stored_log_ids"],
                    "total_logs": immediate_response["total_logs"],
                    "summarization_status": immediate_response["summarization_status"],
                }
            else:
                response_data = immediate_response

            return response_data, 200

        except Exception as e:
            logging.exception(f"Failed to process {source_name} log")
            return {"error": f"Failed to process log: {str(e)}"}, 500

    def _check_pubsub_duplicate(message_id):
        """
        Check if a Pub/Sub message has already been processed.

        Args:
            message_id: The Pub/Sub message ID to check

        Returns:
            tuple: (is_duplicate, response_data) or (False, None) if not duplicate
        """
        if not message_id:
            return False, None

        db_manager = get_db_manager()
        existing = db_manager.query_mongodb(
            collection=config.mongodb_collection,
            query_type="find",
            filter={"pubsub_message_id": message_id},
            limit=1,
        )

        if existing:
            logging.info(
                f"Duplicate Pub/Sub message detected (message_id={message_id}), skipping processing."
            )
            return True, {
                "status": "duplicate",
                "pubsub_message_id": message_id,
                "message": "Pub/Sub message has already been processed",
            }

        return False, None

    def _extract_pubsub_data(pubsub_message):
        """
        Extract and decode data from a Pub/Sub message.

        Args:
            pubsub_message: The Pub/Sub message envelope

        Returns:
            dict: The decoded log data

        Raises:
            ValueError: If the message format is invalid
        """
        if not pubsub_message or "message" not in pubsub_message:
            raise ValueError("Invalid PubSub message format")

        encoded_data = pubsub_message["message"].get("data", "")
        if not encoded_data:
            raise ValueError("No data found in PubSub message")

        # Decode base64 data and parse as JSON
        decoded_data = base64.b64decode(encoded_data).decode("utf-8")
        return json.loads(decoded_data)

    @app.route("/api/log", methods=["POST"])
    def ingest_log():
        """Handle regular API log ingestion."""
        try:
            log_data = request.get_json()
            logging.info(f"Ingesting log via API: {log_data}")

            response_data, status_code = _handle_log_ingestion(log_data, "api")
            return jsonify(response_data), status_code

        except Exception as e:
            logging.exception(f"Failed to process API log")
            return jsonify({"error": f"Failed to process log: {str(e)}"}), 500

    @app.route("/pubsub/api/log", methods=["POST"])
    def ingest_pubsub_log():
        """Handle Pub/Sub log ingestion with idempotency."""
        try:
            pubsub_message = request.get_json()
            logging.info(f"Received PubSub message: {pubsub_message}")

            # Extract message ID for idempotency check
            message_id = pubsub_message.get("message", {}).get(
                "messageId"
            ) or pubsub_message.get("message", {}).get("message_id")

            # Check for duplicate message
            is_duplicate, duplicate_response = _check_pubsub_duplicate(message_id)
            if is_duplicate:
                return jsonify(duplicate_response), 200

            # Extract and decode Pub/Sub data
            log_data = _extract_pubsub_data(pubsub_message)
            logging.info(f"Processing PubSub log data: {log_data}")

            # Process the log data
            response_data, status_code = _handle_log_ingestion(
                log_data, "pubsub", pubsub_message_id=message_id
            )

            return jsonify(response_data), status_code

        except Exception as e:
            logging.exception(f"Failed to process PubSub log")
            return jsonify({"error": f"Failed to process PubSub log: {str(e)}"}), 500

    @app.route("/cache/stats", methods=["GET"])
    def get_cache_statistics():
        """Get session cache statistics for monitoring."""
        try:
            stats = get_cache_stats()
            return jsonify(
                {
                    "cache_stats": stats,
                    "status": "active",
                    "description": "In-memory session cache for improved UI responsiveness",
                }
            )
        except Exception:
            logging.exception(f"Error getting cache stats")
            return jsonify({"error": "Failed to retrieve cache statistics"}), 500

    @app.route("/cache/clear", methods=["POST"])
    def clear_cache():
        """Clear the session cache (for admin/debugging)."""
        try:
            from .utils.session_cache import get_session_cache

            cache = get_session_cache()
            cache.clear()
            return jsonify(
                {"status": "cleared", "message": "Session cache has been cleared"}
            )
        except Exception:
            logging.exception(f"Error clearing cache")
            return jsonify({"error": "Failed to clear cache"}), 500

    @app.route("/cache/cleanup", methods=["POST"])
    def cleanup_cache():
        """Clean up expired entries from cache."""
        try:
            from .utils.session_cache import get_session_cache

            cache = get_session_cache()
            cleaned = cache.cleanup_expired()
            return jsonify(
                {
                    "status": "cleaned",
                    "expired_entries_removed": cleaned,
                    "message": f"Removed {cleaned} expired cache entries",
                }
            )
        except Exception:
            logging.exception(f"Error cleaning cache")
            return jsonify({"error": "Failed to clean cache"}), 500

    @app.route("/sessions/<session_id>/tasks", methods=["GET"])
    def get_session_tasks(session_id):
        """Get current task status for a session - uses LangGraph plan tracking."""
        import time

        start = time.time()

        try:
            agent = get_agent()

            run_id = request.args.get("run_id")

            if run_id:
                # Filter by specific run_id
                task_data = agent.get_session_tasks(session_id, run_id)
            else:
                # Get all tasks from all runs
                task_data = agent.get_all_session_tasks(session_id)

            return jsonify(task_data)

        except Exception:
            logging.exception(
                f"Error getting tasks for session {session_id} after {time.time() - start:.2f}s"
            )
            return jsonify({"error": "Failed to retrieve session tasks"}), 500

    @app.route("/sessions/<session_id>/tasks/archive", methods=["POST"])
    def archive_session_tasks(session_id):
        """Archive and reset tasks for a session - clears LangGraph plan tracking."""
        try:
            agent = get_agent()
            # Clear the session tasks
            if session_id in agent.session_tasks:
                del agent.session_tasks[session_id]

            return jsonify(
                {
                    "session_id": session_id,
                    "status": "archived",
                    "message": "Session plan tracking cleared",
                    "agent_type": "langgraph",
                }
            )

        except Exception:
            logging.exception(f"Error archiving tasks for session {session_id}")
            return jsonify({"error": "Failed to archive session tasks"}), 500

    @app.route("/sessions/<session_id>", methods=["GET"])
    def get_session(session_id):
        """Get a specific session by ID."""
        try:
            db_manager = get_db_manager()
            session_data = db_manager.get_chat_session(session_id)

            if not session_data:
                return jsonify({"error": "Session not found"}), 404

            return jsonify(session_data)

        except Exception:
            logging.exception(f"Error getting session {session_id}")
            return jsonify({"error": "Failed to retrieve session"}), 500

    @app.route("/test/exception", methods=["GET"])
    def test_exception_logging():
        """Test endpoint to demonstrate proper exception logging with stacktraces."""
        try:
            # Intentionally cause an exception to test logging
            raise ValueError(
                "This is a test exception to demonstrate stacktrace logging"
            )

        except Exception:
            logging.exception("Testing exception logging with full stacktrace")
            return (
                jsonify(
                    {
                        "error": "Test exception occurred - check console for full stacktrace",
                        "message": "This endpoint demonstrates proper exception logging",
                    }
                ),
                500,
            )
