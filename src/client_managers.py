"""
Client managers for MongoDB and Google GenAI clients.
Implements singleton patterns to avoid multiple client instances and resource conflicts.
"""

import logging
import threading

import google.genai as genai
from google.cloud import secretmanager
from pymongo import MongoClient
from pymongo.server_api import Server<PERSON>pi

from .env_config import EnvConfig


class MongoClientManager:
    """Thread-safe singleton MongoDB client manager."""

    _instance = None
    _lock = threading.Lock()
    _client = None
    _database = None
    _config = None
    _initialized = False

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, config):
        # Only initialize once, but require config parameter
        if not self._initialized:
            if config is None:
                raise ValueError("MongoClientManager requires a config parameter")
            self._initialize_client(config)
            self._initialized = True
        else:
            # If already initialized, warn about it but don't fail
            logging.warning(
                "MongoClientManager already initialized. "
                "This instance will use the existing configuration."
            )

    def _initialize_client(self, config):
        """Initialize MongoDB client with proper configuration."""
        try:
            self._config = config
            self._client = self._create_mongodb_client()
            self._database = self._client.get_database(self._config.mongodb_db)

            logging.info(
                "MongoDB client initialized successfully with connection pooling"
            )

        except Exception as e:
            logging.error(f"Failed to initialize MongoDB client: {e}")
            raise

    def _create_mongodb_client(self) -> MongoClient:
        """Create MongoDB client with proper connection pool configuration."""
        try:
            # Get credentials from Secret Manager
            secret_client = secretmanager.SecretManagerServiceClient()

            def access_secret(secret_id):
                project_id = self._config.project_id
                name = f"projects/{project_id}/secrets/{secret_id}/versions/latest"
                response = secret_client.access_secret_version(request={"name": name})
                return response.payload.data.decode("UTF-8")

            mongodb_username = access_secret("MONGODB_USERNAME")
            mongodb_password = access_secret("MONGODB_PASSWORD")

            uri = self._config.mongodb_uri.format(
                mongodb_username=mongodb_username,
                mongodb_password=mongodb_password,
            )

            return MongoClient(
                uri,
                server_api=ServerApi("1"),
                maxPoolSize=100,  # Increased pool size for high concurrency
                minPoolSize=10,  # Maintain more ready connections
                maxIdleTimeMS=30000,  # Shorter idle timeout to free connections faster
                waitQueueTimeoutMS=30000,  # Increased wait time for pool checkout
                retryWrites=True,  # Enable retry for write operations
                retryReads=True,  # Enable retry for read operations
                connectTimeoutMS=60000,  # Increased to 60s for SSL handshake issues
                socketTimeoutMS=60000,  # Reduced from 120s to free connections sooner
                serverSelectionTimeoutMS=60000,  # Increased to 60s
                heartbeatFrequencyMS=10000,  # More frequent heartbeats for better pool management
                tlsInsecure=False,  # Ensure secure TLS
                ssl=True,  # Explicitly enable SSL
                maxConnecting=10,  # Limit concurrent connection attempts
            )

        except Exception as e:
            logging.error(f"Failed to create MongoDB client: {e}")
            raise

    def get_client(self) -> MongoClient:
        """Get the MongoDB client instance."""
        if self._client is None:
            raise RuntimeError("MongoDB client not initialized")
        return self._client

    def get_database(self):
        """Get the MongoDB database instance."""
        if self._database is None:
            raise RuntimeError("MongoDB database not initialized")
        return self._database

    def get_config(self) -> EnvConfig:
        """Get the configuration instance."""
        if self._config is None:
            raise RuntimeError("Configuration not initialized")
        return self._config

    def cleanup(self):
        """Clean up MongoDB client resources."""
        if self._client:
            try:
                self._client.close()
                logging.info("MongoDB client closed successfully")
            except Exception as e:
                logging.error(f"Error closing MongoDB client: {e}")
            finally:
                self._client = None
                self._database = None

    def is_connected(self) -> bool:
        """Check if MongoDB client is connected."""
        try:
            if self._client:
                self._client.admin.command("ping")
                return True
        except Exception:
            return False
        return False


class GenAIClientManager:
    """Thread-safe singleton Google GenAI client manager."""

    _instance = None
    _lock = threading.Lock()
    _client = None
    _config = None
    _initialized = False

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, config):
        # Only initialize once, but require config parameter
        if not self._initialized:
            if config is None:
                raise ValueError("GenAIClientManager requires a config parameter")
            self._initialize_client(config)
            self._initialized = True
        else:
            # If already initialized, warn about it but don't fail
            logging.warning(
                "GenAIClientManager already initialized. "
                "This instance will use the existing configuration."
            )

    def _initialize_client(self, config):
        """Initialize Google GenAI client."""
        try:
            self._config = config
            self._client = genai.Client(
                vertexai=True,
                project=self._config.project_id,
                location=self._config.gcp_location,
            )

            logging.info(
                f"Google GenAI client initialized successfully for project "
                f"{self._config.project_id}, location {self._config.gcp_location}"
            )

        except Exception as e:
            logging.error(f"Failed to initialize Google GenAI client: {e}")
            raise

    def get_client(self) -> genai.Client:
        """Get the Google GenAI client instance."""
        if self._client is None:
            raise RuntimeError("Google GenAI client not initialized")
        return self._client

    def get_config(self) -> EnvConfig:
        """Get the configuration instance."""
        if self._config is None:
            raise RuntimeError("Configuration not initialized")
        return self._config

    def cleanup(self):
        """Clean up GenAI client resources."""
        if self._client:
            try:
                # GenAI client doesn't have explicit close method, but we can clear the reference
                self._client = None
                logging.info("Google GenAI client cleaned up successfully")
            except Exception as e:
                logging.error(f"Error cleaning up Google GenAI client: {e}")

    def is_available(self) -> bool:
        """Check if GenAI client is available."""
        try:
            if self._client:
                # Simple test to check if client is responsive
                return True
        except Exception:
            return False
        return False


# Global singleton instances
_mongo_manager = None
_genai_manager = None
_managers_lock = threading.Lock()


def get_mongo_manager(config) -> MongoClientManager:
    """Get the global MongoDB client manager instance."""
    if config is None:
        raise ValueError("get_mongo_manager requires a config parameter")

    global _mongo_manager
    if _mongo_manager is None:
        with _managers_lock:
            if _mongo_manager is None:
                _mongo_manager = MongoClientManager(config)
    return _mongo_manager


def get_genai_manager(config) -> GenAIClientManager:
    """Get the global GenAI client manager instance."""
    if config is None:
        raise ValueError("get_genai_manager requires a config parameter")

    global _genai_manager
    if _genai_manager is None:
        with _managers_lock:
            if _genai_manager is None:
                _genai_manager = GenAIClientManager(config)
    return _genai_manager


def cleanup_all_managers():
    """Clean up all client managers and reset global references.

    This function is intended for:
    1. Application shutdown (registered with atexit)
    2. Testing scenarios where clean state is needed between tests

    Note: After calling this function, new managers will be created
    on the next call to get_mongo_manager() or get_genai_manager().
    """
    global _mongo_manager, _genai_manager

    if _mongo_manager:
        _mongo_manager.cleanup()
        _mongo_manager = None

    if _genai_manager:
        _genai_manager.cleanup()
        _genai_manager = None

    # Reset class-level singleton instances for testing scenarios
    MongoClientManager._instance = None
    GenAIClientManager._instance = None

    logging.info("All client managers cleaned up and singleton state reset")


# Note: cleanup_all_managers is registered in app/__init__.py during Flask app creation
