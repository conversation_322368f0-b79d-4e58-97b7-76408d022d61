import logging
from datetime import datetime

# Removed DatabaseManager import to avoid circular imports


class LoggingUtils:
    """Utility class for comprehensive logging functionality."""

    @staticmethod
    def convert_protobuf_to_dict(obj):
        """Convert protobuf objects to native Python types for MongoDB storage"""
        try:
            if obj is None:
                return None

            # Handle protobuf repeated/list types
            if hasattr(obj, "__iter__") and not isinstance(obj, (str, bytes, dict)):
                try:
                    # Try to convert to list
                    return [LoggingUtils.convert_protobuf_to_dict(item) for item in obj]
                except:
                    # If iteration fails, convert to string
                    return str(obj)

            # Handle protobuf message types
            if hasattr(obj, "__class__") and "proto" in str(type(obj)):
                try:
                    # Try to convert to dict if it has the right methods
                    if hasattr(obj, "_pb"):
                        return str(obj)  # Fallback to string representation
                    elif hasattr(obj, "to_dict"):
                        return obj.to_dict()
                    else:
                        return str(obj)
                except:
                    return str(obj)

            # Handle regular Python types
            if isinstance(obj, (dict, list, tuple, str, int, float, bool)):
                if isinstance(obj, dict):
                    return {
                        k: LoggingUtils.convert_protobuf_to_dict(v)
                        for k, v in obj.items()
                    }
                elif isinstance(obj, (list, tuple)):
                    return [LoggingUtils.convert_protobuf_to_dict(item) for item in obj]
                else:
                    return obj

            # For everything else, convert to string
            return str(obj)

        except Exception as e:
            # If all else fails, return string representation
            return f"<conversion_error: {str(e)}>"

    @staticmethod
    def truncate_for_logging(data, max_length=2000):
        """Truncate data for logging while preserving structure info"""
        if data is None:
            return None

        data_str = str(data)
        if len(data_str) <= max_length:
            return data

        # For large responses, provide summary info
        truncated = data_str[:max_length]

        # Try to parse as JSON to provide structure info
        try:
            if isinstance(data, (list, dict)):
                if isinstance(data, list):
                    summary_info = f"[List with {len(data)} items]"
                else:
                    summary_info = f"[Dict with keys: {list(data.keys())[:10]}]"  # Show first 10 keys
                return {
                    "truncated_content": truncated + "...",
                    "full_length": len(data_str),
                    "structure_info": summary_info,
                }
        except:
            pass

        return {"truncated_content": truncated + "...", "full_length": len(data_str)}

    @staticmethod
    def log_model_response(response, step_name="model_response"):
        """Log model response with comprehensive details including API metrics"""
        try:
            # Extract basic response info
            response_data = {
                "step": step_name,
                "has_candidates": bool(response.candidates),
                "candidate_count": (
                    len(response.candidates) if response.candidates else 0
                ),
            }

            # Extract Vertex AI usage metadata (token counts, etc.)
            if hasattr(response, "usage_metadata") and response.usage_metadata:
                usage_metadata = response.usage_metadata
                api_metrics = {
                    "prompt_token_count": getattr(
                        usage_metadata, "prompt_token_count", None
                    ),
                    "candidates_token_count": getattr(
                        usage_metadata, "candidates_token_count", None
                    ),
                    "total_token_count": getattr(
                        usage_metadata, "total_token_count", None
                    ),
                    "cached_content_token_count": getattr(
                        usage_metadata, "cached_content_token_count", None
                    ),
                }

                # Add detailed token breakdown if available
                if hasattr(usage_metadata, "prompt_tokens_details"):
                    details = usage_metadata.prompt_tokens_details
                    api_metrics["prompt_tokens_details"] = {
                        "audio_tokens": getattr(details, "audio_tokens", None),
                        "cached_content_tokens": getattr(
                            details, "cached_content_tokens", None
                        ),
                        "image_tokens": getattr(details, "image_tokens", None),
                        "text_tokens": getattr(details, "text_tokens", None),
                        "video_tokens": getattr(details, "video_tokens", None),
                    }

                if hasattr(usage_metadata, "candidates_tokens_details"):
                    details = usage_metadata.candidates_tokens_details
                    api_metrics["candidates_tokens_details"] = {
                        "audio_tokens": getattr(details, "audio_tokens", None),
                        "image_tokens": getattr(details, "image_tokens", None),
                        "text_tokens": getattr(details, "text_tokens", None),
                        "video_tokens": getattr(details, "video_tokens", None),
                    }

                response_data["api_metrics"] = api_metrics

                # Calculate cost estimates (rough estimates based on Gemini pricing)
                if api_metrics.get("total_token_count"):
                    # Rough cost calculation (these are example rates, adjust based on actual pricing)
                    input_tokens = api_metrics.get("prompt_token_count", 0) or 0
                    output_tokens = api_metrics.get("candidates_token_count", 0) or 0

                    # Example pricing (per 1M tokens) - update with actual rates
                    input_cost_per_million = 1.25  # USD for Gemini 1.5 Pro input
                    output_cost_per_million = 5.0  # USD for Gemini 1.5 Pro output

                    estimated_cost = (
                        input_tokens / 1_000_000
                    ) * input_cost_per_million + (
                        output_tokens / 1_000_000
                    ) * output_cost_per_million

                    response_data["estimated_cost_usd"] = round(estimated_cost, 6)

            # Extract model version and timing info if available
            if hasattr(response, "model_version"):
                response_data["model_version"] = response.model_version

            if hasattr(response, "create_time"):
                response_data["response_create_time"] = str(response.create_time)

            if response.candidates:
                candidate = response.candidates[0]
                response_data.update(
                    {
                        "finish_reason": LoggingUtils.convert_protobuf_to_dict(
                            getattr(candidate, "finish_reason", None)
                        ),
                        "safety_ratings": LoggingUtils.convert_protobuf_to_dict(
                            getattr(candidate, "safety_ratings", None)
                        ),
                    }
                )

                # Extract content details
                if hasattr(candidate, "content") and candidate.content:
                    from .response_processor import ResponseProcessor

                    texts, tool_calls = (
                        ResponseProcessor.get_texts_and_tool_calls_from_response(
                            response
                        )
                    )
                    response_data.update(
                        {
                            "text_parts_count": len(texts),
                            "tool_calls_count": len(tool_calls),
                            "has_function_calls": bool(tool_calls),
                        }
                    )

                    # Log text content (truncated if needed)
                    if texts:
                        combined_text = "\n".join(texts)
                        response_data["text_content"] = (
                            LoggingUtils.truncate_for_logging(combined_text, 2000)
                        )

                    # Log tool call details
                    if tool_calls:
                        response_data["tool_calls"] = [
                            {
                                "name": tc.name,
                                "args_summary": LoggingUtils.truncate_for_logging(
                                    dict(tc.args), 1000
                                ),
                            }
                            for tc in tool_calls
                        ]

            logging.info(
                f"Model response details: {step_name}", extra={"details": response_data}
            )

            # Log rate limiting information if available
            LoggingUtils.log_api_rate_limiting(response)

        except Exception as e:
            logging.error(f"Error logging model response: {e}")

    @staticmethod
    def log_api_rate_limiting(response):
        """Extract and log rate limiting information if available"""
        try:
            # Try to extract rate limiting headers or metadata if available
            rate_limit_info = {}

            # Check for common rate limiting attributes (these may vary by API)
            if hasattr(response, "_raw_response"):
                raw_response = response._raw_response
                if hasattr(raw_response, "headers"):
                    headers = raw_response.headers
                    # Common rate limiting headers
                    rate_limit_headers = {
                        "x-ratelimit-limit": "requests_per_minute_limit",
                        "x-ratelimit-remaining": "requests_remaining",
                        "x-ratelimit-reset": "reset_time",
                        "retry-after": "retry_after_seconds",
                    }

                    for header, key in rate_limit_headers.items():
                        if header in headers:
                            rate_limit_info[key] = headers[header]

            if rate_limit_info:
                logging.info(
                    "API rate limiting info", extra={"details": rate_limit_info}
                )

        except Exception:
            # Silently ignore rate limiting extraction errors
            pass

    @staticmethod
    def log_tool_execution(tool_name, args, response, execution_time=None, error=None):
        """Log comprehensive tool execution details"""
        log_data = {
            "tool_name": tool_name,
            "execution_timestamp": datetime.utcnow().isoformat(),
            "args": LoggingUtils.truncate_for_logging(args, 1500),
            "execution_time_ms": execution_time,
            "success": error is None,
        }

        if error:
            log_data["error"] = str(error)
            log_data["error_type"] = type(error).__name__
            logging.error("Tool execution failed", extra={"details": log_data})
        else:
            log_data["response"] = LoggingUtils.truncate_for_logging(response, 2000)
            logging.info("Tool execution completed", extra={"details": log_data})

    @staticmethod
    def log_function_responses_to_model(api_responses, iteration_count):
        """Log the formatted function responses being sent to the model"""
        function_responses_summary = []

        for i, api_response in enumerate(api_responses):
            try:
                # Try to extract function_response content
                response_content = ""
                function_name = "unknown"

                if hasattr(api_response, "_pb") and hasattr(
                    api_response._pb, "function_response"
                ):
                    func_resp = api_response._pb.function_response
                    function_name = getattr(func_resp, "name", "unknown")
                    if hasattr(func_resp, "response") and hasattr(
                        func_resp.response, "content"
                    ):
                        response_content = func_resp.response.content
                    elif hasattr(func_resp, "response") and isinstance(
                        func_resp.response, dict
                    ):
                        response_content = func_resp.response.get("content", "")
                else:
                    # Fallback: convert entire response to string
                    response_content = str(api_response)

                function_responses_summary.append(
                    {
                        "index": i + 1,
                        "function_name": function_name,
                        "response_content": LoggingUtils.truncate_for_logging(
                            response_content, 1000
                        ),
                        "response_length": len(str(response_content)),
                    }
                )

            except Exception as e:
                # Fallback for any parsing errors
                function_responses_summary.append(
                    {
                        "index": i + 1,
                        "function_name": "parse_error",
                        "response_content": f"Error parsing response: {e}",
                        "response_length": 0,
                    }
                )

        logging.info(
            "📤 FUNCTION RESPONSES TO MODEL",
            extra={
                "details": {
                    "iteration": iteration_count,
                    "total_responses": len(api_responses),
                    "function_responses": function_responses_summary,
                }
            },
        )


# MongoDBLogHandler moved to src/mongodb_log_handler.py to avoid circular imports
