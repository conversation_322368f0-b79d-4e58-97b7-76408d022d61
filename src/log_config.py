import json
import logging
import sys
import traceback
import uuid

# Global flag to track if logging has been configured
_logging_configured = False


class JsonFormatter(logging.Formatter):
    """Formats log records as JSON strings for file-based logging."""

    def format(self, record):
        log_object = {
            "timestamp": self.formatTime(record, self.datefmt),
            "level": record.levelname,
            "message": record.getMessage(),
            "run_id": getattr(record, "run_id", "N/A"),
        }

        # Add exception information if present
        if record.exc_info:
            log_object["exception"] = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": traceback.format_exception(*record.exc_info),
            }

        if hasattr(record, "details"):
            log_object["details"] = record.details

        # Ensure details are serializable
        try:
            return json.dumps(log_object, indent=2)
        except TypeError:
            log_object["details"] = str(log_object.get("details", ""))
            return json.dumps(log_object, indent=2)


class ConsoleFormatter(logging.Formatter):
    """A more readable formatter for console output with full exception support."""

    def format(self, record):
        run_id = getattr(record, "run_id", "N/A")

        # Format the basic log message
        formatted_message = f"{self.formatTime(record, self.datefmt)} - {record.levelname} - [{run_id}] - {record.getMessage()}"

        # Add exception information if present
        if record.exc_info:
            # Add a separator line for better readability
            formatted_message += "\n" + "=" * 80 + "\n"
            formatted_message += "EXCEPTION DETAILS:\n"
            formatted_message += "=" * 80 + "\n"

            # Add exception type and message
            exc_type, exc_value, exc_tb = record.exc_info
            if exc_type and exc_value:
                formatted_message += f"Exception Type: {exc_type.__name__}\n"
                formatted_message += f"Exception Message: {str(exc_value)}\n"
                formatted_message += "\nFull Traceback:\n"

            # Add the full traceback
            formatted_message += "".join(traceback.format_exception(*record.exc_info))
            formatted_message += "=" * 80

        return formatted_message


class RunIdFilter(logging.Filter):
    """A filter to inject a run_id into records if it is not present."""

    def __init__(self, run_id=None):
        super().__init__()
        self.run_id = run_id

    def filter(self, record):
        if not hasattr(record, "run_id"):
            record.run_id = self.run_id or "N/A"
        return True


def setup_logging(run_id=None, force_reconfigure=False, log_level=logging.INFO):
    """
    Configures logging for the application.
    - A JSON formatter for file output to `audit_trail.log`.
    - A readable formatter for console output with full exception support.
    - A filter to ensure `run_id` is present on all records.

    Args:
        run_id: Optional run ID. If not provided, one will be generated.
        force_reconfigure: If True, will reconfigure even if already configured.
        log_level: Logging level (default: logging.INFO)

    Returns:
        str: The run_id being used
    """
    global _logging_configured

    # Prevent duplicate configuration unless forced
    if _logging_configured and not force_reconfigure:
        return run_id or generate_run_id()

    run_id = run_id or generate_run_id()
    logger = logging.getLogger()

    # Only clear handlers if we're forcing reconfiguration
    if force_reconfigure and logger.hasHandlers():
        logger.handlers.clear()
        logger.filters.clear()

    # Set up only if not already configured
    if not _logging_configured or force_reconfigure:
        logger.setLevel(log_level)

        # Add the run_id filter to the logger
        logger.addFilter(RunIdFilter(run_id))

        # File Handler for the audit trail
        file_handler = logging.FileHandler("audit_trail.log")
        file_handler.setLevel(log_level)
        file_handler.setFormatter(JsonFormatter())
        logger.addHandler(file_handler)

        # Console Handler for real-time monitoring
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        console_handler.setFormatter(
            ConsoleFormatter("%(asctime)s - %(levelname)s - [%(run_id)s] - %(message)s")
        )
        logger.addHandler(console_handler)

        # Suppress noisy Flask access logs (like "127.0.0.1 - - [date] GET /path HTTP/1.1 200 -")
        logging.getLogger("werkzeug").setLevel(logging.WARNING)

        _logging_configured = True

    return run_id


def generate_run_id():
    """Generates a unique ID for a run."""
    return f"run-{uuid.uuid4().hex[:8]}"


def get_logger_for_context(context_name="default", run_id=None):
    """
    Get a logger with a specific context and run_id.
    Useful for Flask routes and other specific contexts.
    """
    if not _logging_configured:
        setup_logging(run_id)

    logger = logging.getLogger(context_name)
    if run_id:
        # Add context-specific run_id filter
        logger.addFilter(RunIdFilter(run_id))

    return logger


def setup_debug_logging():
    """Setup detailed logging for debugging purposes."""
    return setup_logging(log_level=logging.DEBUG, force_reconfigure=True)
