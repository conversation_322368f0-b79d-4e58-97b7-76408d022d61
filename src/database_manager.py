import logging
import time
from datetime import datetime

from bson import ObjectId
from dateutil.parser import isoparse

from .client_managers import get_mongo_manager
from .logging_utils import LoggingUtils


class DatabaseManager:
    """Manages MongoDB operations and connection handling."""

    def __init__(self, config):
        # Use client manager for shared MongoDB connection
        # Require config to ensure consistent configuration
        if config is None:
            raise ValueError("DatabaseManager requires a config parameter")
        self.mongo_manager = get_mongo_manager(config)
        self.config = config
        self.collection_name = (
            self.config.mongodb_collection
        )  # Original collection for general operations
        self.summarized_collection_name = (
            self.config.mongodb_summarized_collection
        )  # Summarized collection with AI summaries
        self.mongodb_client = self.mongo_manager.get_client()
        self.database = self.mongo_manager.get_database()

    def _convert_timestamp_to_datetime(self, value):
        """Convert timestamp string to datetime object."""
        if isinstance(value, str):
            try:
                return isoparse(value)
            except Exception:
                return value
        return value

    def _convert_timestamps_recursive(self, obj):
        """Recursively convert timestamp fields in nested objects."""
        if not isinstance(obj, dict):
            return obj

        result = {}
        for key, value in obj.items():
            # Special handling for $match stage in aggregation pipeline
            if key == "$match":
                result[key] = self._convert_timestamps_recursive(value)
            # Handle timestamp fields (both direct and in paths)
            elif isinstance(value, dict):
                if key.endswith(".timestamp") or key == "timestamp":
                    # If it's a comparison operator dict
                    if any(k.startswith("$") for k in value.keys()):
                        result[key] = {
                            op: self._convert_timestamp_to_datetime(val)
                            for op, val in value.items()
                        }
                    else:
                        result[key] = self._convert_timestamp_to_datetime(value)
                else:
                    result[key] = self._convert_timestamps_recursive(value)
            elif isinstance(value, list):
                result[key] = [
                    (
                        self._convert_timestamps_recursive(item)
                        if isinstance(item, dict)
                        else item
                    )
                    for item in value
                ]
            else:
                result[key] = value
        return result

    def _convert_objectid_strings_recursive(self, obj):
        """Recursively convert string _id values to ObjectId objects for MongoDB queries."""
        if not isinstance(obj, dict):
            return obj

        result = {}
        for key, value in obj.items():
            # Convert _id fields from string to ObjectId
            if key == "_id" and isinstance(value, str):
                try:
                    result[key] = ObjectId(value)
                except Exception:
                    # If conversion fails, keep original value
                    result[key] = value
            # Handle MongoDB operators that might contain _id values
            elif key in ["$in", "$nin"] and isinstance(value, list):
                result[key] = [
                    (
                        ObjectId(item)
                        if isinstance(item, str)
                        and len(item) == 24
                        and all(c in "0123456789abcdefABCDEF" for c in item)
                        else item
                    )
                    for item in value
                ]
            # Handle nested objects recursively
            elif isinstance(value, dict):
                result[key] = self._convert_objectid_strings_recursive(value)
            # Handle lists recursively
            elif isinstance(value, list):
                result[key] = [
                    (
                        self._convert_objectid_strings_recursive(item)
                        if isinstance(item, dict)
                        else item
                    )
                    for item in value
                ]
            else:
                result[key] = value
        return result

    def _convert_objectids_to_strings(self, obj):
        """Recursively convert ObjectId instances to strings for JSON serialization."""
        if isinstance(obj, ObjectId):
            return str(obj)
        elif isinstance(obj, dict):
            return {
                key: self._convert_objectids_to_strings(value)
                for key, value in obj.items()
            }
        elif isinstance(obj, list):
            return [self._convert_objectids_to_strings(item) for item in obj]
        else:
            return obj

    def _normalize_sort_values(self, sort):
        """Normalize sort values to integers (1 or -1) instead of floats."""
        if not sort:
            return sort

        if isinstance(sort, dict):
            # Handle dict format: {"field": 1.0} -> {"field": 1}
            return {key: int(value) for key, value in sort.items()}
        elif isinstance(sort, list):
            # Handle list format: [("field", 1.0)] -> [("field", 1)]
            return [(field, int(direction)) for field, direction in sort]
        else:
            return sort

    def query_mongodb(
        self,
        collection,
        query_type="find",
        filter=None,
        projection=None,
        sort=None,
        limit=None,
        skip=0,
        pipeline=None,
    ):
        """Execute MongoDB query with comprehensive logging and error handling."""

        # Convert timestamps in filter
        if filter:
            filter = self._convert_timestamps_recursive(filter)
            # Convert string _id values to ObjectId objects
            filter = self._convert_objectid_strings_recursive(filter)

        # Convert timestamps and ObjectIds in pipeline
        if pipeline:
            pipeline = [self._convert_timestamps_recursive(stage) for stage in pipeline]
            pipeline = [
                self._convert_objectid_strings_recursive(stage) for stage in pipeline
            ]

        # Normalize sort values to integers
        if sort:
            sort = self._normalize_sort_values(sort)

        # Convert limit to integer if provided
        if limit is not None:
            limit = int(limit)

        col = self.database[collection]

        # Enhanced query logging
        query_details = {
            "collection": collection,
            "query_type": query_type,
            "filter": LoggingUtils.truncate_for_logging(filter, 1000),
            "sort": LoggingUtils.truncate_for_logging(sort, 500),
            "projection": LoggingUtils.truncate_for_logging(projection, 500),
            "skip": skip,
            "limit": limit,
            "pipeline": (
                LoggingUtils.truncate_for_logging(pipeline, 1500) if pipeline else None
            ),
        }

        logging.info("Executing MongoDB query", extra={"details": query_details})

        query_start_time = time.time()

        if query_type == "find":
            cursor = col.find(filter or {}, projection or {})
            if sort:
                # Handle both dict and list formats for sort
                cursor = cursor.sort(
                    list(sort.items()) if isinstance(sort, dict) else sort
                )
            if skip:
                cursor = cursor.skip(skip)
            if limit:
                cursor = cursor.limit(limit)

            results = [doc for doc in cursor]
            query_time = (time.time() - query_start_time) * 1000

            # Convert ObjectIds to strings for JSON serialization
            results = self._convert_objectids_to_strings(results)

            logging.info(
                "MongoDB find query completed",
                extra={
                    "details": {
                        "collection": collection,
                        "result_count": len(results),
                        "query_time_ms": query_time,
                        "had_results": len(results) > 0,
                    }
                },
            )
            return results

        elif query_type == "aggregate":
            if not pipeline:
                raise ValueError("pipeline is required for aggregate queries")

            cursor = col.aggregate(pipeline)
            results = [doc for doc in cursor]
            query_time = (time.time() - query_start_time) * 1000

            # Convert ObjectIds to strings for JSON serialization
            results = self._convert_objectids_to_strings(results)

            logging.info(
                "MongoDB aggregate query completed",
                extra={
                    "details": {
                        "collection": collection,
                        "pipeline_stages": len(pipeline) if pipeline else 0,
                        "result_count": len(results),
                        "query_time_ms": query_time,
                        "had_results": len(results) > 0,
                    }
                },
            )
            return results

        else:
            raise ValueError("Unsupported query_type: must be 'find' or 'aggregate'")

    # Chat Session Management Methods

    def create_chat_session(self, session_id, persona="business", user_id=None):
        """Create a new chat session in MongoDB."""
        try:
            session_data = {
                "session_id": session_id,
                "persona": persona,
                "user_id": user_id,
                "status": "active",
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
                "message_count": 0,
                "last_activity": datetime.utcnow(),
                "title": None,  # Will be set from first message
                "metadata": {},
            }

            result = self.database.chat_sessions.insert_one(session_data)
            logging.info(f"Created chat session: {session_id}")
            return result.inserted_id

        except Exception as e:
            logging.error(f"Failed to create chat session {session_id}: {e}")
            raise

    def store_user_message(self, session_id, content, user_id=None, run_id=None):
        """Store a user message in MongoDB."""
        try:
            # Get next message index
            message_index = self._get_next_message_index(session_id)

            message = {
                "session_id": session_id,
                "message_index": message_index,
                "type": "user",
                "content": content,
                "timestamp": datetime.utcnow(),
                "metadata": {
                    "input_length": len(content),
                    "user_id": user_id,
                    "run_id": run_id,  # Associate user message with run_id
                },
            }

            # Insert message
            result = self.database.chat_messages.insert_one(message)

            # Update session activity and message count
            self.database.chat_sessions.update_one(
                {"session_id": session_id},
                {
                    "$set": {
                        "last_activity": datetime.utcnow(),
                        "updated_at": datetime.utcnow(),
                    },
                    "$inc": {"message_count": 1},
                },
                upsert=True,
            )

            # Set title from first user message if not set
            if message_index == 0:
                title = content[:50] + "..." if len(content) > 50 else content
                self.database.chat_sessions.update_one(
                    {"session_id": session_id}, {"$set": {"title": title}}
                )

            logging.info(f"Stored user message for session {session_id}")
            return result.inserted_id

        except Exception as e:
            logging.error(f"Failed to store user message for session {session_id}: {e}")
            raise

    def store_bot_message(self, session_id, content, run_id=None, metadata=None):
        """Store a bot response message in MongoDB."""
        try:
            message_index = self._get_next_message_index(session_id)

            message_metadata = {"output_length": len(content), "run_id": run_id}
            if metadata:
                message_metadata.update(metadata)

            message = {
                "session_id": session_id,
                "message_index": message_index,
                "type": "bot",
                "content": content,
                "timestamp": datetime.utcnow(),
                "metadata": message_metadata,
            }

            # Insert message
            result = self.database.chat_messages.insert_one(message)

            # Update session activity and message count
            self.database.chat_sessions.update_one(
                {"session_id": session_id},
                {
                    "$set": {
                        "last_activity": datetime.utcnow(),
                        "updated_at": datetime.utcnow(),
                    },
                    "$inc": {"message_count": 1},
                },
            )

            logging.info(f"Stored bot message for session {session_id}")
            return result.inserted_id

        except Exception as e:
            logging.error(f"Failed to store bot message for session {session_id}: {e}")
            raise

    def update_session_completion(self, session_id, metadata=None):
        """Update session status and metadata when completed, preserving run history."""
        try:
            # Get existing session to preserve run history
            session = self.database.chat_sessions.find_one({"session_id": session_id})

            if not session:
                logging.warning(f"Session {session_id} not found for completion update")
                return False

            # Get existing metadata and run history
            existing_metadata = session.get("metadata", {})
            existing_runs = existing_metadata.get("runs", [])

            # Create new run entry if metadata contains run_id
            new_runs = existing_runs.copy()
            if metadata and metadata.get("run_id"):
                new_run_entry = {
                    "run_id": metadata["run_id"],
                    "completed_at": datetime.utcnow(),
                    "runtime_ms": metadata.get("total_runtime_ms"),
                    "success": metadata.get("workflow_success", True),
                    "persona": metadata.get("persona"),
                    "workflow_type": metadata.get("workflow_type"),
                    "steps_executed": metadata.get("steps_executed", 0),
                    "execution_trace": metadata.get(
                        "execution_trace", []
                    ),  # Store execution trace in run metadata
                }

                # Check if this run_id already exists (avoid duplicates)
                existing_run_ids = [run.get("run_id") for run in existing_runs]
                if metadata["run_id"] not in existing_run_ids:
                    new_runs.append(new_run_entry)
                else:
                    # Update existing run entry, preserving existing fields
                    for i, run in enumerate(new_runs):
                        if run.get("run_id") == metadata["run_id"]:
                            # Preserve existing data, only add/update specific completion fields
                            updated_run = run.copy()
                            updated_run.update(
                                {
                                    "completed_at": datetime.utcnow(),
                                    "status": "completed",
                                    "runtime_ms": metadata.get("total_runtime_ms"),
                                    "success": metadata.get("workflow_success", True),
                                    "persona": metadata.get("persona"),
                                    "workflow_type": metadata.get("workflow_type"),
                                    "steps_executed": metadata.get("steps_executed", 0),
                                    "execution_trace": metadata.get(
                                        "execution_trace", []
                                    ),  # Store execution trace in run metadata
                                }
                            )
                            new_runs[i] = updated_run
                            break

            # Build updated metadata preserving history and adding new data
            updated_metadata = {
                **existing_metadata,  # Preserve existing metadata
                "runs": new_runs,  # Store complete run history
                "current_run_id": (
                    metadata.get("run_id")
                    if metadata
                    else existing_metadata.get("current_run_id")
                ),
                "total_runs": len(new_runs),
                "last_update": datetime.utcnow(),
            }

            update_data = {
                "status": "completed",
                "updated_at": datetime.utcnow(),
                "last_activity": datetime.utcnow(),
                "metadata": updated_metadata,
            }

            result = self.database.chat_sessions.update_one(
                {"session_id": session_id}, {"$set": update_data}
            )

            logging.info(
                f"Updated session completion for {session_id} - "
                f"Total runs: {len(new_runs)}, Current run: {metadata.get('run_id') if metadata else 'none'}"
            )
            return result.modified_count > 0

        except Exception as e:
            logging.error(f"Failed to update session completion for {session_id}: {e}")
            raise

    def get_chat_session(self, session_id):
        """Retrieve complete chat session data from MongoDB."""
        try:
            # Get session info
            session = self.database.chat_sessions.find_one({"session_id": session_id})
            if not session:
                return None

            # Convert ObjectIds to strings for JSON serialization
            session = self._convert_objectids_to_strings(session)

            # Get all messages for this session
            messages_cursor = self.database.chat_messages.find(
                {"session_id": session_id}
            ).sort("message_index", 1)

            messages = list(messages_cursor)

            # Convert ObjectIds to strings for JSON serialization
            messages = self._convert_objectids_to_strings(messages)

            # Import markdown renderer
            try:
                from app.utils.markdown_utils import render_markdown
            except ImportError:
                logging.warning(
                    "Markdown renderer not available, bot messages will not be formatted"
                )
                render_markdown = None

            # Convert to the expected chat format
            history = []
            for msg in messages:
                content = msg["content"]

                # Process bot messages through markdown renderer
                if msg["type"] == "bot" and render_markdown:
                    try:
                        content = render_markdown(content)
                    except Exception as e:
                        logging.error(
                            f"Error rendering markdown for message {msg.get('message_index', 'unknown')}: {e}"
                        )
                        # Fall back to original content if markdown rendering fails
                        content = msg["content"]

                msg_data = {
                    "type": msg["type"],
                    "content": content,
                    "run_id": msg.get("metadata", {}).get("run_id"),
                    "timestamp": msg.get("timestamp"),
                    "message_index": msg.get("message_index"),
                }

                # Include execution trace for bot messages
                if msg["type"] == "bot" and msg.get("metadata", {}).get(
                    "execution_trace"
                ):
                    msg_data["execution_trace"] = msg.get("metadata", {}).get(
                        "execution_trace"
                    )

                history.append(msg_data)

            chat_data = {
                "session_id": session_id,  # Include the session_id in the returned data
                "title": session.get("title", f"Session {session_id[:8]}..."),
                "history": history,
                "persona": session.get("persona", "business"),
                "feedback": [],  # TODO: Implement feedback storage
                "archived": session.get("status") == "archived",
                "status": session.get("status"),  # Include status for consistency
                "user_id": session.get("user_id"),
                "session_metadata": session.get("metadata", {}),
                "created_at": session.get("created_at"),
                "updated_at": session.get("updated_at"),
                "message_count": session.get("message_count", 0),
                "log_summary": session.get("metadata", {}).get("log_summary", {}),
                "learned_from": session.get(
                    "learned_from", False
                ),  # Include learned_from field
                "learned_at": session.get("learned_at"),  # Include learned_at timestamp
            }

            return chat_data

        except Exception as e:
            logging.error(f"Failed to retrieve chat session {session_id}: {e}")
            return None

    def _get_next_message_index(self, session_id):
        """Get the next message index for a session."""
        try:
            # Count existing messages for this session
            count = self.database.chat_messages.count_documents(
                {"session_id": session_id}
            )
            return count
        except Exception as e:
            logging.error(f"Failed to get next message index for {session_id}: {e}")
            return 0

    def ensure_chat_indexes(self):
        """Ensure proper indexes exist for chat collections."""
        try:
            # Chat sessions indexes
            self.database.chat_sessions.create_index("session_id", unique=True)
            self.database.chat_sessions.create_index("user_id")
            self.database.chat_sessions.create_index("created_at")
            self.database.chat_sessions.create_index("last_activity")

            # Chat messages indexes
            self.database.chat_messages.create_index(
                [("session_id", 1), ("message_index", 1)], unique=True
            )
            self.database.chat_messages.create_index("timestamp")
            self.database.chat_messages.create_index("type")
            self.database.chat_messages.create_index(
                "metadata.run_id"
            )  # Index for run_id queries

            # Session logs indexes
            self.database.session_logs.create_index(
                [("session_id", 1), ("timestamp", 1)]
            )
            self.database.session_logs.create_index("run_id")
            self.database.session_logs.create_index("log_level")
            self.database.session_logs.create_index("message_type")

            logging.info("Chat indexes ensured")

        except Exception as e:
            logging.error(f"Failed to ensure chat indexes: {e}")
            raise

    def store_session_log(self, session_id, log_entry, run_id=None):
        """Store an agent log entry for a session in MongoDB."""
        try:
            # Import here to avoid circular imports
            from .logging_utils import LoggingUtils

            # Prepare log document with protobuf conversion
            log_doc = {
                "session_id": session_id,
                "run_id": run_id,
                "timestamp": datetime.utcnow(),
                "log_level": log_entry.get("levelname", "INFO"),
                "message": log_entry.get("message", ""),
                "message_type": self._classify_log_message(
                    log_entry.get("message", "")
                ),
                "details": LoggingUtils.convert_protobuf_to_dict(
                    log_entry.get("details", {})
                ),
                "source": "intelligent_agent",
                "raw_log": LoggingUtils.convert_protobuf_to_dict(log_entry),
            }

            # Store log entry
            result = self.database.session_logs.insert_one(log_doc)

            # Update session's log summary
            self._update_session_log_summary(session_id, log_entry)

            return result.inserted_id

        except Exception as e:
            logging.error(f"Failed to store session log for {session_id}: {e}")
            raise

    def _classify_log_message(self, message):
        """Classify log message type for better organization."""
        message_lower = message.lower()

        if "api call" in message_lower or "model response" in message_lower:
            return "api_interaction"
        elif "mongodb" in message_lower or "query" in message_lower:
            return "database_operation"
        elif "tool" in message_lower and (
            "execution" in message_lower or "call" in message_lower
        ):
            return "tool_execution"
        elif "iteration" in message_lower:
            return "processing_iteration"
        elif "final answer" in message_lower or "summary" in message_lower:
            return "response_generation"
        elif "error" in message_lower or "failed" in message_lower:
            return "error"
        elif "agent run" in message_lower:
            return "session_lifecycle"
        else:
            return "general"

    def _update_session_log_summary(self, session_id, log_entry):
        """Update session metadata with log summary information."""
        try:
            # Get current session
            session = self.database.chat_sessions.find_one({"session_id": session_id})
            if not session:
                return

            # Initialize log summary if doesn't exist
            current_metadata = session.get("metadata", {})
            log_summary = current_metadata.get(
                "log_summary",
                {
                    "total_logs": 0,
                    "error_count": 0,
                    "api_calls": 0,
                    "tool_executions": 0,
                    "iterations": 0,
                    "by_level": {},
                    "by_type": {},
                },
            )

            # Update counters
            log_summary["total_logs"] += 1

            # Count by level
            level = log_entry.get("levelname", "INFO")
            log_summary["by_level"][level] = log_summary["by_level"].get(level, 0) + 1

            # Count by message type
            message_type = self._classify_log_message(log_entry.get("message", ""))
            log_summary["by_type"][message_type] = (
                log_summary["by_type"].get(message_type, 0) + 1
            )

            # Specific counters
            message = log_entry.get("message", "").lower()
            if "error" in level.lower() or "error" in message:
                log_summary["error_count"] += 1
            if "api call" in message or "model response" in message:
                log_summary["api_calls"] += 1
            if "tool" in message and "execution" in message:
                log_summary["tool_executions"] += 1
            if "iteration" in message and "completed" in message:
                log_summary["iterations"] += 1

            # Update session metadata
            current_metadata["log_summary"] = log_summary

            self.database.chat_sessions.update_one(
                {"session_id": session_id},
                {
                    "$set": {
                        "metadata": current_metadata,
                        "updated_at": datetime.utcnow(),
                    }
                },
            )

        except Exception as e:
            logging.error(f"Failed to update session log summary for {session_id}: {e}")

    def get_session_logs(self, session_id, limit=100, log_types=None):
        """Retrieve logs for a session from MongoDB."""
        try:
            # Build filter
            filter_query = {"session_id": session_id}
            if log_types:
                filter_query["message_type"] = {"$in": log_types}

            # Query logs
            logs_cursor = (
                self.database.session_logs.find(filter_query)
                .sort("timestamp", 1)
                .limit(limit)
            )

            logs = list(logs_cursor)

            # Convert to expected format
            formatted_logs = []
            for log in logs:
                formatted_log = log.get("raw_log", {})
                formatted_log["source_file"] = "mongodb"
                formatted_log["_id"] = str(log["_id"])
                formatted_log["message_type"] = log.get("message_type")
                formatted_logs.append(formatted_log)

            return formatted_logs

        except Exception as e:
            logging.error(f"Failed to retrieve session logs for {session_id}: {e}")
            return []

    def get_session_run_ids(self, session_id):
        """Get all run_ids for a session from chat_sessions metadata."""
        try:
            session = self.database.chat_sessions.find_one({"session_id": session_id})

            if not session or not session.get("metadata", {}).get("runs"):
                return []

            # Return just the run_ids as strings
            return [run["run_id"] for run in session["metadata"]["runs"]]

        except Exception as e:
            logging.error(f"Failed to get run_ids for session {session_id}: {e}")
            return []

    def get_session_runs(self, session_id):
        """Get all run history for a session with full metadata."""
        try:
            session = self.database.chat_sessions.find_one({"session_id": session_id})

            if not session or not session.get("metadata", {}).get("runs"):
                return []

            # Return the full runs array with all metadata
            return session["metadata"]["runs"]

        except Exception as e:
            logging.error(f"Failed to get session runs for {session_id}: {e}")
            return []

    def get_session_by_run_id(self, session_id, run_id):
        """Get messages and logs for a specific run within a session."""
        try:
            # Get messages for this run
            messages_cursor = self.database.chat_messages.find(
                {"session_id": session_id, "metadata.run_id": run_id}
            ).sort("message_index", 1)

            messages = list(messages_cursor)

            # Get logs for this run
            logs_cursor = self.database.session_logs.find(
                {"session_id": session_id, "run_id": run_id}
            ).sort("timestamp", 1)

            logs = list(logs_cursor)

            # Convert ObjectIds to strings for JSON serialization
            messages = self._convert_objectids_to_strings(messages)
            logs = self._convert_objectids_to_strings(logs)

            # Import markdown renderer
            try:
                from app.utils.markdown_utils import render_markdown
            except ImportError:
                logging.warning(
                    "Markdown renderer not available, bot messages will not be formatted"
                )
                render_markdown = None

            # Process bot messages through markdown renderer
            if render_markdown:
                for msg in messages:
                    if msg.get("type") == "bot":
                        try:
                            msg["content"] = render_markdown(msg["content"])
                        except Exception as e:
                            logging.error(
                                f"Error rendering markdown for message {msg.get('message_index', 'unknown')}: {e}"
                            )
                            # Leave original content if markdown rendering fails

            return {
                "session_id": session_id,
                "run_id": run_id,
                "messages": messages,
                "logs": logs,
                "message_count": len(messages),
                "log_count": len(logs),
            }

        except Exception as e:
            logging.error(f"Failed to get run {run_id} for session {session_id}: {e}")
            return None

    def get_session_with_run_details(self, session_id):
        """Get complete session data organized by run_id."""
        try:
            # Get base session data
            chat_data = self.get_chat_session(session_id)
            if not chat_data:
                return None

            # Get all run_ids for this session
            run_ids = self.get_session_run_ids(session_id)

            # Group messages and logs by run_id
            runs_data = {}
            for run_id in run_ids:
                runs_data[run_id] = self.get_session_by_run_id(session_id, run_id)

            # Enhanced session data
            chat_data["runs"] = runs_data
            chat_data["run_ids"] = run_ids
            chat_data["total_runs"] = len(run_ids)

            return chat_data

        except Exception as e:
            logging.error(
                f"Failed to get session with run details for {session_id}: {e}"
            )
            return None

    def archive_chat_session(self, session_id):
        """Archive a chat session by setting its status to 'archived'."""
        try:
            result = self.database.chat_sessions.update_one(
                {"session_id": session_id},
                {
                    "$set": {
                        "status": "archived",
                        "updated_at": datetime.utcnow(),
                        "archived_at": datetime.utcnow(),
                    }
                },
            )

            if result.modified_count > 0:
                logging.info(f"Archived chat session: {session_id}")
                return True
            else:
                logging.warning(f"Session {session_id} not found for archiving")
                return False

        except Exception as e:
            logging.error(f"Failed to archive chat session {session_id}: {e}")
            raise

    def restore_chat_session(self, session_id):
        """Restore an archived chat session by setting its status to 'active'."""
        try:
            result = self.database.chat_sessions.update_one(
                {"session_id": session_id, "status": "archived"},
                {
                    "$set": {"status": "active", "updated_at": datetime.utcnow()},
                    "$unset": {"archived_at": ""},
                },
            )

            if result.modified_count > 0:
                logging.info(f"Restored chat session: {session_id}")
                return True
            else:
                logging.warning(
                    f"Archived session {session_id} not found for restoration"
                )
                return False

        except Exception as e:
            logging.error(f"Failed to restore chat session {session_id}: {e}")
            raise

    def store_run_tasks(self, session_id, run_id, tasks):
        """Store tasks for a specific run in the chat_sessions metadata."""
        try:
            # First, ensure the run exists in metadata.runs
            session = self.database.chat_sessions.find_one({"session_id": session_id})
            if not session:
                logging.error(f"Session {session_id} not found")
                return False

            existing_metadata = session.get("metadata", {})
            existing_runs = existing_metadata.get("runs", [])

            # Find the run and update its tasks
            updated_runs = existing_runs.copy()
            run_found = False

            for i, run in enumerate(updated_runs):
                if run.get("run_id") == run_id:
                    # Preserve existing run data, only update tasks
                    updated_runs[i]["tasks"] = tasks
                    updated_runs[i]["tasks_updated_at"] = datetime.utcnow()
                    # Preserve created_at, persona, and other existing fields
                    run_found = True
                    break

            # If run doesn't exist, create it
            if not run_found:
                new_run = {
                    "run_id": run_id,
                    "created_at": datetime.utcnow(),
                    "tasks": tasks,
                    "tasks_updated_at": datetime.utcnow(),
                    "status": "active",
                }
                updated_runs.append(new_run)

            # Update the session with new runs array
            update_fields = {
                "metadata.runs": updated_runs,
                "updated_at": datetime.utcnow(),
            }

            # Only set as current run if this is a new run
            if not run_found:
                update_fields["metadata.run_id"] = run_id
                logging.info(
                    f"Setting {run_id} as current run for session {session_id}"
                )

            result = self.database.chat_sessions.update_one(
                {"session_id": session_id}, {"$set": update_fields}
            )

            logging.info(
                f"Stored {len(tasks)} tasks for run {run_id} in session {session_id}"
            )
            return result.modified_count > 0

        except Exception as e:
            logging.error(
                f"Failed to store run tasks for session {session_id}, run {run_id}: {e}"
            )
            return False

    def create_run_entry(self, session_id, run_id, persona="business"):
        """Create a new run entry immediately when a query is received."""
        try:
            session = self.database.chat_sessions.find_one({"session_id": session_id})
            if not session:
                logging.error(f"Session {session_id} not found")
                return False

            # Get existing runs
            existing_metadata = session.get("metadata", {})
            existing_runs = existing_metadata.get("runs", [])

            # Check if run already exists
            for run in existing_runs:
                if run.get("run_id") == run_id:
                    logging.info(
                        f"Run {run_id} already exists for session {session_id}"
                    )
                    return True

            # Create new run entry with created_at timestamp
            new_run = {
                "run_id": run_id,
                "created_at": datetime.utcnow(),
                "status": "active",
                "persona": persona,
            }

            # Add to runs array
            updated_runs = existing_runs + [new_run]

            # Update session
            update_fields = {
                "metadata.runs": updated_runs,
                "metadata.current_run_id": run_id,  # Set as current run
                "updated_at": datetime.utcnow(),
            }

            result = self.database.chat_sessions.update_one(
                {"session_id": session_id}, {"$set": update_fields}
            )

            logging.info(f"Created run entry {run_id} for session {session_id}")
            return result.modified_count > 0

        except Exception as e:
            logging.error(
                f"Failed to create run entry for session {session_id}, run {run_id}: {e}"
            )
            return False

    def get_run_tasks(self, session_id, run_id=None):
        """Get tasks for a specific run, or current run if run_id not specified."""
        try:
            session = self.database.chat_sessions.find_one({"session_id": session_id})
            if not session:
                return None

            runs = session.get("metadata", {}).get("runs", [])

            # If no run_id specified, use the current run
            if run_id is None:
                run_id = session.get("metadata", {}).get("run_id")

            if not run_id:
                # If still no run_id, use the most recent run
                if runs:
                    # Get most recent run (assuming runs are ordered by creation)
                    latest_run = max(
                        runs, key=lambda x: x.get("created_at", datetime.min)
                    )
                    run_id = latest_run.get("run_id")
                else:
                    return None

            # Find the specific run
            for run in runs:
                if run.get("run_id") == run_id:
                    return {
                        "session_id": session_id,
                        "run_id": run_id,
                        "tasks": run.get("tasks", []),
                        "run_metadata": run,
                        "current_run_id": session.get("metadata", {}).get("run_id"),
                    }

            return None

        except Exception as e:
            logging.error(
                f"Failed to get run tasks for session {session_id}, run {run_id}: {e}"
            )
            return None

    def get_all_session_run_tasks(self, session_id):
        """Get tasks from all runs for a session."""
        try:
            session = self.database.chat_sessions.find_one({"session_id": session_id})
            if not session:
                return None

            runs = session.get("metadata", {}).get("runs", [])
            all_tasks = []
            runs_with_tasks = {}

            for run in runs:
                run_id = run.get("run_id")
                tasks = run.get("tasks", [])

                # Add run_id to each task for identification
                for task in tasks:
                    task_with_run = dict(task)
                    task_with_run["run_id"] = run_id
                    all_tasks.append(task_with_run)

                runs_with_tasks[run_id] = {"tasks": tasks, "metadata": run}

            return {
                "session_id": session_id,
                "runs": runs_with_tasks,
                "all_tasks": all_tasks,
                "current_run_id": session.get("metadata", {}).get("run_id"),
                "total_runs": len(runs),
            }

        except Exception as e:
            logging.error(f"Failed to get all run tasks for session {session_id}: {e}")
            return None

    def clear_current_run_id(self, session_id):
        """Clear the current run_id for a session when run completes."""
        try:
            result = self.database.chat_sessions.update_one(
                {"session_id": session_id},
                {
                    "$unset": {"metadata.run_id": ""},
                    "$set": {"updated_at": datetime.utcnow()},
                },
            )

            if result.modified_count > 0:
                logging.info(f"Cleared current run_id for session {session_id}")
                return True
            else:
                logging.warning(f"No session found to clear run_id for {session_id}")
                return False

        except Exception as e:
            logging.error(
                f"Failed to clear current run_id for session {session_id}: {e}"
            )
            return False

    def close_connection(self):
        """Close MongoDB connection and cleanup resources."""
        if hasattr(self, "mongo_manager") and self.mongo_manager:
            self.mongo_manager.cleanup()
            logging.info("MongoDB connection closed via client manager")
